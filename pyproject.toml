[project]
name = "underlines-adk"
version = "0.0.1"
description = "Underlines with Agents"
authors = [
    { name = "<PERSON>", email = "<EMAIL>" },
]
license = { text = "Proprietary" }
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "google-adk (>=1.2.1,<2.0.0)",
    "exa-py (>=1.14.5,<2.0.0)",
    "litellm (>=1.72.1,<2.0.0)",
]
optional-dependencies = { dev = [
    "black (>=25.1.0,<26.0.0)",
    "flake8 (>=7.2.0,<8.0.0)",
    "pre-commit (>=4.2.0,<5.0.0)",
    "click (>=8.2.1,<9.0.0)",
    "isort (>=6.0.1,<7.0.0)",
] }

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.group.dev.dependencies]
python-dotenv = "^1.1.0"
pytest-asyncio = "^1.0.0"

[tool.pytest.ini_options]
filterwarnings = [
    # Ignore deprecation warnings from Pydantic V2, which are triggered by google-adk.
    # We can't fix these ourselves, so we filter them to keep test output clean.
    "ignore::pydantic.PydanticDeprecatedSince20",
]
