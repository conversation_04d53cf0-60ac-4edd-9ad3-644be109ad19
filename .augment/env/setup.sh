#!/bin/bash
set -e

# Update system packages
sudo apt-get update

# Install Python 3.13 and pip if not available
if ! command -v python3.13 &> /dev/null; then
    sudo apt-get install -y software-properties-common
    sudo add-apt-repository -y ppa:deadsnakes/ppa
    sudo apt-get update
    sudo apt-get install -y python3.13 python3.13-venv python3.13-dev
fi

# Install pip for Python 3.13
if ! python3.13 -m pip --version &> /dev/null; then
    curl -sS https://bootstrap.pypa.io/get-pip.py | python3.13
fi

# Install Poetry
if ! command -v poetry &> /dev/null; then
    curl -sSL https://install.python-poetry.org | python3.13 -
    echo 'export PATH="$HOME/.local/bin:$PATH"' >> $HOME/.profile
    export PATH="$HOME/.local/bin:$PATH"
fi

# Navigate to the workspace directory
cd /mnt/persist/workspace

# Configure Poetry to use Python 3.13
poetry env use python3.13

# Install dependencies including dev dependencies
poetry install --with dev

# Add poetry environment to PATH for test execution
POETRY_VENV_PATH=$(poetry env info --path)
echo "export PATH=\"$POETRY_VENV_PATH/bin:\$PATH\"" >> $HOME/.profile
export PATH="$POETRY_VENV_PATH/bin:$PATH"

# Verify installation
poetry run python --version
poetry run pytest --version