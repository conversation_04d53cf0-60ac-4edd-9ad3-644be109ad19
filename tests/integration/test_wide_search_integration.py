"""
Integration test for WideSearchAgent using the real Exa API.

This test will only run if the EXA_API_KEY environment variable is set. It sends a real query to the agent and checks that the output is a list of stories, each with the required fields.
"""

import os

import pytest
from dotenv import load_dotenv

# Load environment variables from .env if present
load_dotenv()

from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService
from google.genai import types

# Import the agent and required ADK classes
from underlines_adk.agents.wide_search_agent import exa_agent


@pytest.mark.skipif(
    not os.getenv("EXA_API_KEY"),
    reason="EXA_API_KEY not set; skipping integration test.",
)
@pytest.mark.asyncio
async def test_wide_search_agent_real_api():
    """
    Sends a real query to the WideSearchAgent and checks the structure of the output.
    """
    # Set up session service and create a session
    session_service = InMemorySessionService()
    session = await session_service.create_session(
        app_name="wide_search_test", user_id="test_user"
    )

    # Create runner with the agent
    runner = Runner(
        agent=exa_agent, app_name="wide_search_test", session_service=session_service
    )

    # Create the query content
    query = "latest AI news"
    lookback_days = 7
    user_content = types.Content(
        role="user",
        parts=[
            types.Part(text=f'{{"query": "{query}", "lookback_days": {lookback_days}}}')
        ],
    )

    # Run the agent and collect final response
    final_response_content = None
    async for event in runner.run_async(
        user_id="test_user", session_id=session.id, new_message=user_content
    ):
        if event.is_final_response():
            final_response_content = event.content
            break

    # Verify we got a response
    assert final_response_content is not None, "Should have received a final response"
    assert final_response_content.parts, "Response should have parts"
    response_text = final_response_content.parts[0].text
    assert response_text, "Response should have text content"

    print(f"Agent response: {response_text}")

    # The agent should return structured output with wide_search_results
    # Since this is a real API test, we'll do basic validation that makes sense
    assert len(response_text) > 0, "Response should not be empty"

    # Check that the response appears to be about AI news
    # (This is a simple heuristic check for integration testing)
    response_lower = response_text.lower()
    ai_keywords = ["ai", "artificial intelligence", "machine learning", "llm", "model"]
    assert any(keyword in response_lower for keyword in ai_keywords), (
        "Response should contain AI-related keywords"
    )
