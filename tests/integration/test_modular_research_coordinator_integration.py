"""
Integration tests for the modular DynamicResearchCoordinator workflow.

Tests the complete story-centric workflow through all three phases:
- Phase 1: Story Discovery (StoryDiscoveryPhase)
- Phase 2: Story Validation (StoryValidationPhase)  
- Phase 3: Deep Research (DeepResearchPhase)

Validates session state management, phase coordination, and end-to-end workflow.
"""

import pytest
from unittest.mock import MagicMock, AsyncMock, patch

from underlines_adk.agents.dynamic_research_coordinator import DynamicResearchCoordinator
from google.adk.events import Event, EventActions


class TestModularResearchCoordinatorIntegration:
    """Integration test suite for modular DynamicResearchCoordinator workflow."""

    @pytest.mark.asyncio
    @patch('underlines_adk.agents.phases.story_discovery_phase.llm')
    @patch('underlines_adk.agents.phases.story_discovery_phase.wide_search_agent')
    @patch('underlines_adk.agents.phases.story_discovery_phase.ParallelAgent')
    @patch('underlines_adk.agents.phases.story_validation_phase.llm')
    @patch('underlines_adk.agents.phases.deep_research_phase.ParallelAgent')
    async def test_complete_workflow_execution(self, mock_deep_parallel, mock_validation_llm, 
                                             mock_discovery_parallel, mock_wide_search, mock_discovery_llm):
        """Test complete workflow execution through all three phases."""
        coordinator = DynamicResearchCoordinator()
        
        # Mock session context
        mock_ctx = MagicMock()
        mock_ctx.session.state = {
            "user_requirements": "Daily tech newsletter focusing on AI developments",
            "research_plan": "Research latest AI trends and breakthroughs"
        }
        
        # Mock Phase 1: Story Discovery
        mock_discovery_llm_response = MagicMock()
        mock_discovery_llm_response.generations = [[MagicMock()]]
        mock_discovery_llm_response.generations[0][0].text = "AI developments\nMachine learning\nTech startups"
        mock_discovery_llm.agenerate = AsyncMock(return_value=mock_discovery_llm_response)
        
        mock_discovery_parallel_instance = MagicMock()
        mock_discovery_parallel.return_value = mock_discovery_parallel_instance
        
        async def mock_discovery_parallel_run(ctx):
            # Simulate WideSearchAgent results
            yield Event(author="WideSearchAgent", actions=EventActions(
                state_delta={"wide_search_results": [{"title": "AI breakthrough", "summary": "Major AI development"}]}
            ))
        
        mock_discovery_parallel_instance.run_async = mock_discovery_parallel_run
        
        # Mock Phase 2: Story Validation
        mock_validation_llm_response = MagicMock()
        mock_validation_llm_response.generations = [[MagicMock()]]
        mock_validation_llm_response.generations[0][0].text = "SUFFICIENT - Stories align well with AI focus"
        mock_validation_llm.agenerate = AsyncMock(return_value=mock_validation_llm_response)
        
        # Mock Phase 3: Deep Research
        mock_deep_parallel_instance = MagicMock()
        mock_deep_parallel.return_value = mock_deep_parallel_instance
        
        async def mock_deep_parallel_run(ctx):
            # Simulate LoopAgent results
            yield Event(author="LoopAgent", actions=EventActions(
                state_delta={"deep_research_results": "Detailed AI research"}
            ))
        
        mock_deep_parallel_instance.run_async = mock_deep_parallel_run
        
        # Execute complete workflow
        events = []
        async for event in coordinator._run_async_impl(mock_ctx):
            events.append(event)
        
        # Verify workflow execution
        assert len(events) >= 5  # Events from all phases
        
        # Verify Phase 1 execution
        mock_discovery_llm.agenerate.assert_called_once()
        mock_discovery_parallel.assert_called_once()
        
        # Verify Phase 2 execution
        mock_validation_llm.agenerate.assert_called_once()
        
        # Verify Phase 3 execution
        mock_deep_parallel.assert_called_once()
        
        # Verify session state progression
        state_updates = {}
        for event in events:
            if event.actions and event.actions.state_delta:
                state_updates.update(event.actions.state_delta)
        
        # Should have PRD-defined session state keys
        expected_keys = ["topic_areas", "discovered_stories", "final_story_list", "story_research_outputs"]
        for key in expected_keys:
            assert key in state_updates or any(key in event.actions.state_delta for event in events 
                                             if event.actions and event.actions.state_delta)

    @pytest.mark.asyncio
    async def test_workflow_error_propagation(self):
        """Test that errors in phases are properly propagated."""
        coordinator = DynamicResearchCoordinator()
        
        # Mock context with missing research plan
        mock_ctx = MagicMock()
        mock_ctx.session.state = {
            "user_requirements": "Some requirements"
            # Missing research_plan
        }
        
        # Should raise error from coordinator validation
        with pytest.raises(ValueError, match="No research plan found for research coordination"):
            async for _ in coordinator._run_async_impl(mock_ctx):
                pass

    @pytest.mark.asyncio
    @patch('underlines_adk.agents.phases.story_discovery_phase.llm')
    async def test_workflow_handles_phase_failures(self, mock_llm):
        """Test that workflow handles failures in individual phases."""
        coordinator = DynamicResearchCoordinator()
        
        # Mock context with valid initial state
        mock_ctx = MagicMock()
        mock_ctx.session.state = {
            "user_requirements": "Daily tech newsletter",
            "research_plan": "Research tech trends"
        }
        
        # Mock LLM failure in Phase 1
        mock_llm.agenerate = AsyncMock(side_effect=Exception("API timeout"))
        
        # Should raise error from Phase 1
        with pytest.raises(ValueError, match="Failed to identify topic areas: API timeout"):
            async for _ in coordinator._run_async_impl(mock_ctx):
                pass

    @pytest.mark.asyncio
    @patch('underlines_adk.agents.phases.story_discovery_phase.llm')
    @patch('underlines_adk.agents.phases.story_discovery_phase.wide_search_agent')
    @patch('underlines_adk.agents.phases.story_discovery_phase.ParallelAgent')
    async def test_session_state_management_across_phases(self, mock_parallel, mock_wide_search, mock_llm):
        """Test proper session state management across all phases."""
        coordinator = DynamicResearchCoordinator()
        
        # Mock session context that will be updated
        session_state = {
            "user_requirements": "Biotech newsletter focusing on TL1A research",
            "research_plan": "Deep research on TL1A developments"
        }
        mock_ctx = MagicMock()
        mock_ctx.session.state = session_state
        
        # Mock Phase 1: Story Discovery
        mock_llm_response = MagicMock()
        mock_llm_response.generations = [[MagicMock()]]
        mock_llm_response.generations[0][0].text = "TL1A research\nTL1A clinical trials\nTL1A companies"
        mock_llm.agenerate = AsyncMock(return_value=mock_llm_response)
        
        mock_parallel_instance = MagicMock()
        mock_parallel.return_value = mock_parallel_instance
        
        # Track session state updates
        state_updates = []
        
        async def mock_parallel_run(ctx):
            # Simulate session state update
            update = {"wide_search_results": "search results"}
            state_updates.append(update)
            yield Event(author="ParallelAgent", actions=EventActions(state_delta=update))
        
        mock_parallel_instance.run_async = mock_parallel_run
        
        # Execute just Phase 1 to test state management
        events = []
        async for event in coordinator.story_discovery_phase.execute(
            mock_ctx, "Biotech newsletter focusing on TL1A research", "Deep research on TL1A developments"
        ):
            events.append(event)
        
        # Verify session state keys are used correctly
        topic_areas_event = None
        discovered_stories_event = None
        
        for event in events:
            if event.actions and event.actions.state_delta:
                if "topic_areas" in event.actions.state_delta:
                    topic_areas_event = event
                if "discovered_stories" in event.actions.state_delta:
                    discovered_stories_event = event
        
        # Verify PRD-defined session state keys are used
        assert topic_areas_event is not None
        assert discovered_stories_event is not None
        
        # Verify topic areas were identified correctly
        topic_areas = topic_areas_event.actions.state_delta["topic_areas"]
        assert len(topic_areas) == 3
        assert "TL1A research" in topic_areas

    def test_modular_architecture_maintainability(self):
        """Test that modular architecture improves maintainability."""
        coordinator = DynamicResearchCoordinator()
        
        # Each phase should be a separate class instance
        assert coordinator.story_discovery_phase is not coordinator.story_validation_phase
        assert coordinator.story_validation_phase is not coordinator.deep_research_phase
        assert coordinator.deep_research_phase is not coordinator.story_discovery_phase
        
        # Each phase should have its own execute method
        assert hasattr(coordinator.story_discovery_phase, 'execute')
        assert hasattr(coordinator.story_validation_phase, 'execute')
        assert hasattr(coordinator.deep_research_phase, 'execute')
        
        # Phases should be independently testable
        assert callable(coordinator.story_discovery_phase.execute)
        assert callable(coordinator.story_validation_phase.execute)
        assert callable(coordinator.deep_research_phase.execute)

    def test_backward_compatibility_with_existing_agents(self):
        """Test that refactored coordinator maintains compatibility with existing agents."""
        coordinator = DynamicResearchCoordinator()
        
        # Should still be a BaseAgent
        from google.adk.agents import BaseAgent
        assert isinstance(coordinator, BaseAgent)
        
        # Should have the same interface as before
        assert hasattr(coordinator, 'name')
        assert hasattr(coordinator, 'description')
        assert hasattr(coordinator, '_run_async_impl')
        
        # Should maintain the same configuration patterns
        assert coordinator.sub_agents == []  # Phases handle coordination
        
        # Should work with existing ADK patterns
        assert coordinator.name == "DynamicResearchCoordinator"
        assert "modular" in coordinator.description.lower()

    def test_anti_patterns_avoided_in_integration(self):
        """Test that the integrated workflow avoids documented anti-patterns."""
        coordinator = DynamicResearchCoordinator()
        
        # Should not have hardcoded newsletter-specific logic
        assert not hasattr(coordinator, '_daily_news_workflow')
        assert not hasattr(coordinator, '_biotech_workflow')
        
        # Should not have programmatic quality assessment
        assert not hasattr(coordinator, '_calculate_quality_score')
        
        # Should not have custom agent wrappers
        assert not hasattr(coordinator, '_create_context_aware_agent')
        
        # Should use modular phase architecture
        phase_classes = [
            type(coordinator.story_discovery_phase).__name__,
            type(coordinator.story_validation_phase).__name__,
            type(coordinator.deep_research_phase).__name__
        ]
        
        expected_phases = ["StoryDiscoveryPhase", "StoryValidationPhase", "DeepResearchPhase"]
        for expected_phase in expected_phases:
            assert expected_phase in phase_classes
