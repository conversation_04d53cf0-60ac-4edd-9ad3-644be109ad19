# Integration Tests for Underlines ADK

This directory contains integration tests for the Underlines ADK agents and tools. Integration tests verify that components work together as expected, using real external services where appropriate.

## Structure
- `test_wide_search_integration.py`: Integration test for the WideSearchAgent using the real Exa API.
- `test_deep_search_integration.py`: Integration test for the DeepSearchAgent using the real Exa API and LLM.

## Best Practices
- Integration tests may require environment variables (e.g., `EXA_API_KEY`) to be set.
- Tests should be skipped gracefully if required environment variables are missing.
- Tests should check for correct structure and content of outputs.

## Running the Tests

To run all integration tests in this directory:

```bash
pytest
```

Or to run only the tests in this directory:

```bash
pytest tests/integration/
```

## Adding New Tests
- Place new integration test files in this directory.
- Clearly document any required environment variables or setup steps.
- Use descriptive names and docstrings for all test functions. 