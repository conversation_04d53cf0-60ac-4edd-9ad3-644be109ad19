"""
Integration test for DeepSearchAgent using the real Exa API.

This test will only run if the EXA_API_KEY environment variable is set. It sends a real query to the agent and checks that the output is a comprehensive analysis with proper citations.
"""

import os
import re

import pytest
from dotenv import load_dotenv

# Load environment variables from .env if present
load_dotenv()

from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService
from google.genai import types

# Import the agent and required ADK classes
from underlines_adk.agents.deep_search_agent import exa_agent


@pytest.mark.skipif(
    not os.getenv("EXA_API_KEY"),
    reason="EXA_API_KEY not set; skipping integration test.",
)
@pytest.mark.asyncio
async def test_deep_search_agent_real_api():
    """
    Sends a real query to the DeepSearchAgent and checks the structure and quality of the output.
    """
    # Set up session service and create a session
    session_service = InMemorySessionService()
    session = await session_service.create_session(
        app_name="deep_search_test", user_id="test_user"
    )

    # Create runner with the agent
    runner = Runner(
        agent=exa_agent, app_name="deep_search_test", session_service=session_service
    )

    # Create the query content for a specific story/topic
    query = "OpenAI ChatGPT o3 model announcement"
    lookback_days = 14
    user_content = types.Content(
        role="user",
        parts=[
            types.Part(text=f'{{"query": "{query}", "lookback_days": {lookback_days}}}')
        ],
    )

    # Run the agent and collect final response
    final_response_content = None
    async for event in runner.run_async(
        user_id="test_user", session_id=session.id, new_message=user_content
    ):
        if event.is_final_response():
            final_response_content = event.content
            break

    # Verify we got a response
    assert final_response_content is not None, "Should have received a final response"
    assert final_response_content.parts, "Response should have parts"
    response_text = final_response_content.parts[0].text
    assert response_text, "Response should have text content"

    print(f"Agent response length: {len(response_text)} characters")
    print(f"Agent response preview: {response_text[:500]}...")

    # Basic validation
    assert len(response_text) > 500, "Deep search response should be comprehensive (>500 chars)"

    # Check for citations - should have markdown links or numbered references
    citation_patterns = [
        r'\[.*?\]\(https?://.*?\)',  # [text](url) format
        r'\[\d+\]',  # [1], [2], etc. format
        r'https?://[^\s]+',  # Direct URLs
    ]
    
    has_citations = any(re.search(pattern, response_text) for pattern in citation_patterns)
    assert has_citations, "Response should contain citations in markdown format"

    # Check that the response appears to be about the specific topic
    response_lower = response_text.lower()
    topic_keywords = ["openai", "chatgpt", "o3", "model"]
    assert any(keyword in response_lower for keyword in topic_keywords), (
        "Response should contain keywords related to the specific topic"
    )

    # Check for analysis structure indicators
    structure_indicators = ["#", "##", "background", "analysis", "context", "implications"]
    has_structure = any(indicator in response_lower for indicator in structure_indicators)
    assert has_structure, "Response should have structured analysis with headings or sections"

    # Verify it's more than just a summary - should be comprehensive
    comprehensive_indicators = ["comprehensive", "detailed", "in-depth", "analysis", "perspective"]
    has_depth = any(indicator in response_lower for indicator in comprehensive_indicators)
    assert has_depth, "Response should indicate comprehensive, in-depth analysis"


@pytest.mark.skipif(
    not os.getenv("EXA_API_KEY"),
    reason="EXA_API_KEY not set; skipping integration test.",
)
@pytest.mark.asyncio
async def test_deep_search_agent_citation_quality():
    """
    Test that the deep search agent provides high-quality citations.
    """
    # Set up session service and create a session
    session_service = InMemorySessionService()
    session = await session_service.create_session(
        app_name="deep_search_citation_test", user_id="test_user"
    )

    # Create runner with the agent
    runner = Runner(
        agent=exa_agent, app_name="deep_search_citation_test", session_service=session_service
    )

    # Create the query content
    query = "latest developments in AI safety research"
    lookback_days = 7
    user_content = types.Content(
        role="user",
        parts=[
            types.Part(text=f'{{"query": "{query}", "lookback_days": {lookback_days}}}')
        ],
    )

    # Run the agent and collect final response
    final_response_content = None
    async for event in runner.run_async(
        user_id="test_user", session_id=session.id, new_message=user_content
    ):
        if event.is_final_response():
            final_response_content = event.content
            break

    # Verify we got a response
    assert final_response_content is not None
    response_text = final_response_content.parts[0].text

    # Count different types of citations
    markdown_links = len(re.findall(r'\[.*?\]\(https?://.*?\)', response_text))
    numbered_refs = len(re.findall(r'\[\d+\]', response_text))
    direct_urls = len(re.findall(r'https?://[^\s\)]+', response_text))

    total_citations = markdown_links + numbered_refs + direct_urls
    
    # Should have multiple citations for a comprehensive analysis
    assert total_citations >= 3, f"Should have at least 3 citations, found {total_citations}"
    
    print(f"Citation analysis:")
    print(f"  Markdown links: {markdown_links}")
    print(f"  Numbered references: {numbered_refs}")
    print(f"  Direct URLs: {direct_urls}")
    print(f"  Total citations: {total_citations}")

    # Verify URLs are valid format
    urls = re.findall(r'https?://[^\s\)]+', response_text)
    for url in urls[:5]:  # Check first 5 URLs
        assert url.startswith(('http://', 'https://')), f"Invalid URL format: {url}"
