"""
Unit tests for DeepSearchAgent (exa_agent).

These tests mock exa_deep_search and the LLM model to verify correct formatting, citation requirements, and error handling.
"""

import os
from unittest.mock import MagicMock, patch

import pytest
from dotenv import load_dotenv

# Load environment variables from .env if present
load_dotenv()

from underlines_adk.agents import deep_search_agent


@pytest.fixture
def mock_exa_deep_search():
    with patch("underlines_adk.agents.deep_search_agent.exa_deep_search") as mock:
        mock.return_value = {
            "type": "exa",
            "results": [
                {
                    "url": "http://authoritative-source.com/story",
                    "title": "Comprehensive Analysis of Topic",
                    "published": "2024-06-01",
                    "text": "Full text content with detailed information about the topic...",
                },
                {
                    "url": "http://expert-analysis.com/deep-dive",
                    "title": "Expert Perspective on Topic",
                    "published": "2024-06-02",
                    "text": "Expert analysis providing additional context and insights...",
                },
                {
                    "url": "http://news-source.com/latest",
                    "title": "Latest Developments in Topic",
                    "published": "2024-06-03",
                    "text": "Recent updates and developments related to the topic...",
                },
            ],
        }
        yield mock


@pytest.fixture
def mock_runner_and_session():
    """Mock the ADK Runner and SessionService for testing."""
    with (
        patch("google.adk.runners.Runner") as mock_runner_class,
        patch(
            "google.adk.sessions.InMemorySessionService"
        ) as mock_session_service_class,
    ):
        # Mock session service
        mock_session_service = MagicMock()
        mock_session = MagicMock()
        mock_session.id = "test_deep_search_agent_session_id"
        mock_session.state = {}
        mock_session_service.create_session.return_value = mock_session
        mock_session_service.get_session.return_value = mock_session
        mock_session_service_class.return_value = mock_session_service

        # Mock runner
        mock_runner = MagicMock()
        mock_runner_class.return_value = mock_runner

        yield {
            "runner": mock_runner,
            "session_service": mock_session_service,
            "session": mock_session,
            "runner_class": mock_runner_class,
            "session_service_class": mock_session_service_class,
        }


def test_agent_has_correct_configuration():
    """
    Test that the agent is properly configured with expected attributes.
    """
    agent = deep_search_agent.exa_agent
    assert agent.name == "DeepSearchAgent"  # Actual name from the agent
    assert "exa_deep_search" in str(agent.tools)  # Check tools are configured
    assert (
        agent.output_key == "deep_search_results"
    )  # Check output key is configured correctly
    
    # Verify the instruction contains key requirements for deep search
    instruction = agent.instruction
    assert "in-depth analysis" in instruction
    assert "citations" in instruction
    assert "comprehensive" in instruction


def test_agent_with_runner_success(mock_exa_deep_search, mock_runner_and_session):
    """
    Test successful agent execution through Runner with mocked tools.
    """
    mocks = mock_runner_and_session

    # Mock successful event stream with final response containing citations
    mock_event = MagicMock()
    mock_event.is_final_response.return_value = True
    mock_event.content.parts = [MagicMock()]
    mock_event.content.parts[
        0
    ].text = '''# Comprehensive Analysis of OpenAI ChatGPT o3 Model

## Background and Context

OpenAI announced the release of their new ChatGPT o3 model [1](http://authoritative-source.com/story), representing a significant advancement in their AI capabilities. This development follows their previous o1 model series [2](http://expert-analysis.com/deep-dive).

## Key Developments

The o3 model introduces several improvements over its predecessors, including enhanced reasoning capabilities and better performance on complex tasks [1](http://authoritative-source.com/story). Recent updates indicate widespread availability is planned for early 2024 [3](http://news-source.com/latest).

## References

[1] http://authoritative-source.com/story
[2] http://expert-analysis.com/deep-dive  
[3] http://news-source.com/latest'''

    mocks["runner"].run_async.return_value = [mock_event]

    # Simulate agent execution
    from google.adk.runners import Runner
    from google.adk.sessions import InMemorySessionService
    from google.genai import types

    # This will use our mocked classes
    session_service = InMemorySessionService()
    session = session_service.create_session(app_name="test", user_id="test")
    runner = Runner(
        agent=deep_search_agent.exa_agent,
        app_name="test",
        session_service=session_service,
    )

    user_content = types.Content(
        role="user", parts=[types.Part(text='{"query": "OpenAI ChatGPT o3 model release", "lookback_days": 7}')]
    )
    events = runner.run_async(
        user_id="test", session_id=session.id, new_message=user_content
    )

    # Verify the runner was called correctly
    mocks["runner"].run_async.assert_called_once()

    # Process events
    final_response = None
    for event in events:
        if event.is_final_response():
            final_response = event.content.parts[0].text

    assert final_response is not None
    
    # Verify the response contains citations
    assert "[1]" in final_response or "](http" in final_response
    assert "http://" in final_response  # Should contain URLs

    # Verify exa_deep_search would have been called (through the agent's tool execution)
    assert mock_exa_deep_search.return_value["type"] == "exa"


def test_agent_handles_exceptions(mock_runner_and_session):
    """
    Test that the agent handles exceptions from exa_deep_search gracefully.
    """
    mocks = mock_runner_and_session

    # Mock an error event
    mock_event = MagicMock()
    mock_event.is_final_response.return_value = True
    mock_event.content.parts = [MagicMock()]
    mock_event.content.parts[0].text = "Error: Unable to conduct deep search - API call failed"

    mocks["runner"].run_async.return_value = [mock_event]

    with patch(
        "underlines_adk.agents.deep_search_agent.exa_deep_search",
        side_effect=Exception("API error"),
    ):
        from google.adk.runners import Runner
        from google.adk.sessions import InMemorySessionService
        from google.genai import types

        session_service = InMemorySessionService()
        session = session_service.create_session(app_name="test", user_id="test")
        runner = Runner(
            agent=deep_search_agent.exa_agent,
            app_name="test",
            session_service=session_service,
        )

        user_content = types.Content(
            role="user",
            parts=[types.Part(text='{"query": "fail", "lookback_days": 7}')],
        )

        # This should not raise an exception - the agent should handle it gracefully
        events = runner.run_async(
            user_id="test", session_id=session.id, new_message=user_content
        )

        # Verify we can process the events without error
        event_count = 0
        for event in events:
            event_count += 1

        assert event_count > 0  # Should get at least one event back


def test_agent_deep_search_data_structure(mock_exa_deep_search):
    """
    Test that the deep search returns full text content for in-depth analysis.
    """
    result = mock_exa_deep_search.return_value
    
    # Verify we get fewer results but with full text content
    assert len(result["results"]) <= 5  # Deep search should return fewer, more detailed results
    
    # Verify each result has full text content for analysis
    for item in result["results"]:
        assert "text" in item  # Should have full text content
        assert len(item["text"]) > 50  # Should have substantial content
        assert "url" in item  # Should have source URLs for citations
