# Unit Tests for Underlines ADK

This directory contains unit tests for the Underlines ADK agents and tools. Unit tests are designed to verify the correctness of individual components in isolation, using mocks and fixtures for external dependencies.

## Structure
- `test_wide_search_agent.py`: Unit tests for the WideSearchAgent, including formatting, deduplication, and error handling.
- `test_deep_search_agent.py`: Unit tests for the DeepSearchAgent, including citation requirements and comprehensive analysis.
- `test_logged_session_service.py`: Unit tests for the LoggedSessionService wrapper functionality.

## Best Practices
- All external dependencies (APIs, models) are mocked.
- Each test is independent and repeatable.
- Tests cover normal, edge, and error cases.

## Running the Tests

To run all unit tests in this directory:

```bash
pytest
```

Or to run only the tests in this directory:

```bash
pytest tests/unit/
```

## Adding New Tests
- Place new unit test files in this directory.
- Use descriptive names and docstrings for all test functions.
- Mock all external dependencies. 