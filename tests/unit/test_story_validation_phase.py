"""
Unit tests for StoryValidationPhase.

Tests the Phase 2 implementation of the story-centric workflow:
- Story validation against user requirements using LLM intelligence
- Iteration capability for story discovery refinement
- Session state management with PRD-defined keys
- Error handling and validation
"""

import pytest
from unittest.mock import MagicMock, AsyncMock, patch

from underlines_adk.agents.phases.story_validation_phase import StoryValidationPhase
from google.adk.events import Event, EventActions


class TestStoryValidationPhase:
    """Test suite for StoryValidationPhase implementation."""

    def test_phase_initialization(self):
        """Test that the phase initializes correctly."""
        phase = StoryValidationPhase()
        assert phase.coordinator_name == "DynamicResearchCoordinator"
        
        # Test with custom coordinator name
        custom_phase = StoryValidationPhase(coordinator_name="CustomCoordinator")
        assert custom_phase.coordinator_name == "CustomCoordinator"

    @pytest.mark.asyncio
    async def test_execute_raises_error_for_missing_user_requirements(self):
        """Test that execute raises ValueError when user requirements are missing."""
        phase = StoryValidationPhase()
        mock_ctx = MagicMock()
        
        # Should raise error for empty user requirements
        with pytest.raises(ValueError, match="No user requirements provided for story validation"):
            async for _ in phase.execute(mock_ctx, ""):
                pass

    @pytest.mark.asyncio
    async def test_execute_raises_error_for_missing_discovered_stories(self):
        """Test that execute raises ValueError when discovered stories are missing."""
        phase = StoryValidationPhase()
        mock_ctx = MagicMock()
        mock_ctx.session.state = {}  # No discovered_stories
        
        # Should raise error for missing discovered stories
        with pytest.raises(ValueError, match="No discovered stories found for validation"):
            async for _ in phase.execute(mock_ctx, "Some requirements"):
                pass

    @pytest.mark.asyncio
    @patch('underlines_adk.agents.phases.story_validation_phase.llm')
    async def test_execute_successful_validation_sufficient(self, mock_llm):
        """Test successful execution when stories are sufficient."""
        phase = StoryValidationPhase()
        mock_ctx = MagicMock()
        
        # Mock discovered stories in session state
        discovered_stories = [
            {"title": "AI breakthrough", "summary": "Major AI development"},
            {"title": "Tech startup funding", "summary": "New funding round"}
        ]
        mock_ctx.session.state = {"discovered_stories": discovered_stories}
        
        # Mock LLM response indicating stories are sufficient
        mock_llm_response = MagicMock()
        mock_llm_response.generations = [[MagicMock()]]
        mock_llm_response.generations[0][0].text = "SUFFICIENT - These stories align well with the tech newsletter requirements"
        mock_llm.agenerate = AsyncMock(return_value=mock_llm_response)
        
        # Execute the phase
        events = []
        async for event in phase.execute(mock_ctx, "Daily tech newsletter"):
            events.append(event)
        
        # Verify we got one event with final story list
        assert len(events) == 1
        
        # Verify final story list event
        final_event = events[0]
        assert final_event.author == "DynamicResearchCoordinator"
        assert "final_story_list" in final_event.actions.state_delta
        assert final_event.actions.state_delta["final_story_list"] == discovered_stories

    @pytest.mark.asyncio
    @patch('underlines_adk.agents.phases.story_validation_phase.llm')
    async def test_execute_successful_validation_insufficient(self, mock_llm):
        """Test successful execution when stories are insufficient."""
        phase = StoryValidationPhase()
        mock_ctx = MagicMock()
        
        # Mock discovered stories in session state
        discovered_stories = [
            {"title": "Random story", "summary": "Not relevant"}
        ]
        mock_ctx.session.state = {"discovered_stories": discovered_stories}
        
        # Mock LLM response indicating stories are insufficient
        mock_llm_response = MagicMock()
        mock_llm_response.generations = [[MagicMock()]]
        mock_llm_response.generations[0][0].text = "NEEDS_IMPROVEMENT - Stories don't match tech focus"
        mock_llm.agenerate = AsyncMock(return_value=mock_llm_response)
        
        # Execute the phase
        events = []
        async for event in phase.execute(mock_ctx, "Daily tech newsletter"):
            events.append(event)
        
        # Verify we got one event (for initial implementation, proceeds with discovered stories)
        assert len(events) == 1
        
        # Verify final story list event (uses discovered stories as fallback)
        final_event = events[0]
        assert final_event.author == "DynamicResearchCoordinator"
        assert "final_story_list" in final_event.actions.state_delta
        assert final_event.actions.state_delta["final_story_list"] == discovered_stories

    @pytest.mark.asyncio
    @patch('underlines_adk.agents.phases.story_validation_phase.llm')
    async def test_validate_stories_against_requirements_sufficient(self, mock_llm):
        """Test story validation when stories are sufficient."""
        phase = StoryValidationPhase()
        
        # Mock LLM response indicating sufficient
        mock_llm_response = MagicMock()
        mock_llm_response.generations = [[MagicMock()]]
        mock_llm_response.generations[0][0].text = "SUFFICIENT - These stories provide excellent coverage of AI developments"
        mock_llm.agenerate = AsyncMock(return_value=mock_llm_response)
        
        # Test stories
        stories = [{"title": "AI breakthrough", "summary": "Major development"}]
        
        # Call validation
        result = await phase._validate_stories_against_requirements(stories, "AI newsletter")
        
        # Verify result
        assert result["sufficient"] is True
        assert result["stories"] == stories
        assert "SUFFICIENT" in result["assessment"]

    @pytest.mark.asyncio
    @patch('underlines_adk.agents.phases.story_validation_phase.llm')
    async def test_validate_stories_against_requirements_insufficient(self, mock_llm):
        """Test story validation when stories are insufficient."""
        phase = StoryValidationPhase()
        
        # Mock LLM response indicating insufficient
        mock_llm_response = MagicMock()
        mock_llm_response.generations = [[MagicMock()]]
        mock_llm_response.generations[0][0].text = "NEEDS_IMPROVEMENT - Stories lack focus on biotech"
        mock_llm.agenerate = AsyncMock(return_value=mock_llm_response)
        
        # Test stories
        stories = [{"title": "Random news", "summary": "Not biotech related"}]
        
        # Call validation
        result = await phase._validate_stories_against_requirements(stories, "Biotech newsletter")
        
        # Verify result
        assert result["sufficient"] is False
        assert result["stories"] == stories
        assert "NEEDS_IMPROVEMENT" in result["assessment"]

    @pytest.mark.asyncio
    @patch('underlines_adk.agents.phases.story_validation_phase.llm')
    async def test_validate_stories_handles_llm_failure(self, mock_llm):
        """Test that LLM failures are handled with clear error messages."""
        phase = StoryValidationPhase()
        
        # Mock LLM failure
        mock_llm.agenerate = AsyncMock(side_effect=Exception("API timeout"))
        
        # Should raise error with clear message
        with pytest.raises(ValueError, match="Failed to validate stories against requirements: API timeout"):
            await phase._validate_stories_against_requirements([], "Some requirements")

    def test_format_stories_for_analysis_with_dict_stories(self):
        """Test story formatting for LLM analysis with dictionary stories."""
        phase = StoryValidationPhase()
        
        stories = [
            {"title": "Story 1", "summary": "Summary 1", "url": "http://example.com"},
            {"title": "Story 2", "summary": "Summary 2"}
        ]
        
        formatted = phase._format_stories_for_analysis(stories)
        
        # Verify formatting
        assert "Story 1:" in formatted
        assert "Story 2:" in formatted
        assert "title: Story 1" in formatted
        assert "summary: Summary 1" in formatted
        assert "url: http://example.com" in formatted

    def test_format_stories_for_analysis_with_string_stories(self):
        """Test story formatting for LLM analysis with string stories."""
        phase = StoryValidationPhase()
        
        stories = ["Story content 1", "Story content 2"]
        
        formatted = phase._format_stories_for_analysis(stories)
        
        # Verify formatting
        assert "Story 1:" in formatted
        assert "Story 2:" in formatted
        assert "Content: Story content 1" in formatted
        assert "Content: Story content 2" in formatted

    def test_format_stories_for_analysis_empty_list(self):
        """Test story formatting with empty story list."""
        phase = StoryValidationPhase()
        
        formatted = phase._format_stories_for_analysis([])
        
        # Should return appropriate message
        assert formatted == "No stories discovered"

    def test_phase_follows_agentic_principles(self):
        """Test that the phase follows agentic principles from documentation."""
        phase = StoryValidationPhase()
        
        # Should not have hardcoded quality criteria
        assert not hasattr(phase, '_quality_thresholds')
        assert not hasattr(phase, '_validation_rules')
        
        # Should use LLM intelligence for validation
        assert hasattr(phase, '_validate_stories_against_requirements')
        
        # Should trust LLM judgment completely (no programmatic scoring)
        # This is verified by checking the implementation uses LLM response directly

    @pytest.mark.asyncio
    @patch('underlines_adk.agents.phases.story_validation_phase.llm')
    async def test_validation_prompt_structure(self, mock_llm):
        """Test that the validation prompt is properly structured."""
        phase = StoryValidationPhase()
        
        # Mock LLM response
        mock_llm_response = MagicMock()
        mock_llm_response.generations = [[MagicMock()]]
        mock_llm_response.generations[0][0].text = "SUFFICIENT - Good stories"
        mock_llm.agenerate = AsyncMock(return_value=mock_llm_response)
        
        # Test stories and requirements
        stories = [{"title": "AI news", "summary": "AI development"}]
        requirements = "Daily AI newsletter for developers"
        
        # Call validation
        await phase._validate_stories_against_requirements(stories, requirements)
        
        # Verify prompt structure
        call_args = mock_llm.agenerate.call_args
        prompt = call_args[1]["messages"][0]["content"]
        
        # Should include user requirements and discovered stories
        assert "Daily AI newsletter for developers" in prompt
        assert "AI news" in prompt
        
        # Should provide clear evaluation criteria
        assert "EVALUATION TASK" in prompt
        assert "professional judgment" in prompt
        
        # Should specify response format
        assert "RESPONSE FORMAT" in prompt
        assert "SUFFICIENT" in prompt
        assert "NEEDS_IMPROVEMENT" in prompt
