"""
Error handling tests for the modular DynamicResearchCoordinator and phase classes.

Tests various error scenarios to ensure proper error handling, clear error messages,
and fail-fast behavior as documented in ANTI_PATTERNS_AND_VALIDATION.md.
"""

import pytest
from unittest.mock import MagicMock, AsyncMock, patch

from underlines_adk.agents.dynamic_research_coordinator import DynamicResearchCoordinator
from underlines_adk.agents.phases.story_discovery_phase import StoryDiscoveryPhase
from underlines_adk.agents.phases.story_validation_phase import StoryValidationPhase
from underlines_adk.agents.phases.deep_research_phase import DeepResearchPhase


class TestErrorHandlingScenarios:
    """Test suite for error handling across all components."""

    # DynamicResearchCoordinator Error Handling
    @pytest.mark.asyncio
    async def test_coordinator_missing_user_requirements(self):
        """Test coordinator error handling for missing user requirements."""
        coordinator = DynamicResearchCoordinator()
        mock_ctx = MagicMock()
        mock_ctx.session.state = {"research_plan": "Some plan"}  # Missing user_requirements
        
        with pytest.raises(ValueError, match="No user requirements found for research coordination"):
            async for _ in coordinator._run_async_impl(mock_ctx):
                pass

    @pytest.mark.asyncio
    async def test_coordinator_missing_research_plan(self):
        """Test coordinator error handling for missing research plan."""
        coordinator = DynamicResearchCoordinator()
        mock_ctx = MagicMock()
        mock_ctx.session.state = {"user_requirements": "Some requirements"}  # Missing research_plan
        
        with pytest.raises(ValueError, match="No research plan found for research coordination"):
            async for _ in coordinator._run_async_impl(mock_ctx):
                pass

    @pytest.mark.asyncio
    async def test_coordinator_empty_session_state(self):
        """Test coordinator error handling for completely empty session state."""
        coordinator = DynamicResearchCoordinator()
        mock_ctx = MagicMock()
        mock_ctx.session.state = {}  # Empty state
        
        with pytest.raises(ValueError, match="No user requirements found for research coordination"):
            async for _ in coordinator._run_async_impl(mock_ctx):
                pass

    # StoryDiscoveryPhase Error Handling
    @pytest.mark.asyncio
    async def test_story_discovery_missing_user_requirements(self):
        """Test StoryDiscoveryPhase error handling for missing user requirements."""
        phase = StoryDiscoveryPhase()
        mock_ctx = MagicMock()
        
        with pytest.raises(ValueError, match="No user requirements provided for story discovery"):
            async for _ in phase.execute(mock_ctx, "", "research plan"):
                pass

    @pytest.mark.asyncio
    async def test_story_discovery_missing_research_plan(self):
        """Test StoryDiscoveryPhase error handling for missing research plan."""
        phase = StoryDiscoveryPhase()
        mock_ctx = MagicMock()
        
        with pytest.raises(ValueError, match="No research plan provided for story discovery"):
            async for _ in phase.execute(mock_ctx, "user requirements", ""):
                pass

    @pytest.mark.asyncio
    @patch('underlines_adk.agents.phases.story_discovery_phase.llm')
    async def test_story_discovery_llm_api_failure(self, mock_llm):
        """Test StoryDiscoveryPhase error handling for LLM API failures."""
        phase = StoryDiscoveryPhase()
        mock_ctx = MagicMock()
        
        # Mock LLM API failure
        mock_llm.agenerate = AsyncMock(side_effect=Exception("API rate limit exceeded"))
        
        with pytest.raises(ValueError, match="Failed to identify topic areas: API rate limit exceeded"):
            async for _ in phase.execute(mock_ctx, "user requirements", "research plan"):
                pass

    @pytest.mark.asyncio
    @patch('underlines_adk.agents.phases.story_discovery_phase.llm')
    async def test_story_discovery_empty_llm_response(self, mock_llm):
        """Test StoryDiscoveryPhase error handling for empty LLM responses."""
        phase = StoryDiscoveryPhase()
        mock_ctx = MagicMock()
        
        # Mock empty LLM response
        mock_llm_response = MagicMock()
        mock_llm_response.generations = [[MagicMock()]]
        mock_llm_response.generations[0][0].text = "   \n\n   "  # Only whitespace
        mock_llm.agenerate = AsyncMock(return_value=mock_llm_response)
        
        with pytest.raises(ValueError, match="LLM failed to identify any topic areas"):
            async for _ in phase.execute(mock_ctx, "user requirements", "research plan"):
                pass

    # StoryValidationPhase Error Handling
    @pytest.mark.asyncio
    async def test_story_validation_missing_user_requirements(self):
        """Test StoryValidationPhase error handling for missing user requirements."""
        phase = StoryValidationPhase()
        mock_ctx = MagicMock()
        
        with pytest.raises(ValueError, match="No user requirements provided for story validation"):
            async for _ in phase.execute(mock_ctx, ""):
                pass

    @pytest.mark.asyncio
    async def test_story_validation_missing_discovered_stories(self):
        """Test StoryValidationPhase error handling for missing discovered stories."""
        phase = StoryValidationPhase()
        mock_ctx = MagicMock()
        mock_ctx.session.state = {}  # No discovered_stories
        
        with pytest.raises(ValueError, match="No discovered stories found for validation"):
            async for _ in phase.execute(mock_ctx, "user requirements"):
                pass

    @pytest.mark.asyncio
    async def test_story_validation_empty_discovered_stories(self):
        """Test StoryValidationPhase error handling for empty discovered stories list."""
        phase = StoryValidationPhase()
        mock_ctx = MagicMock()
        mock_ctx.session.state = {"discovered_stories": []}  # Empty list
        
        with pytest.raises(ValueError, match="No discovered stories found for validation"):
            async for _ in phase.execute(mock_ctx, "user requirements"):
                pass

    @pytest.mark.asyncio
    @patch('underlines_adk.agents.phases.story_validation_phase.llm')
    async def test_story_validation_llm_failure(self, mock_llm):
        """Test StoryValidationPhase error handling for LLM failures."""
        phase = StoryValidationPhase()
        mock_ctx = MagicMock()
        mock_ctx.session.state = {"discovered_stories": [{"title": "test"}]}
        
        # Mock LLM failure
        mock_llm.agenerate = AsyncMock(side_effect=Exception("Network timeout"))
        
        with pytest.raises(ValueError, match="Failed to validate stories against requirements: Network timeout"):
            async for _ in phase.execute(mock_ctx, "user requirements"):
                pass

    # DeepResearchPhase Error Handling
    @pytest.mark.asyncio
    async def test_deep_research_missing_user_requirements(self):
        """Test DeepResearchPhase error handling for missing user requirements."""
        phase = DeepResearchPhase()
        mock_ctx = MagicMock()
        
        with pytest.raises(ValueError, match="No user requirements provided for deep research"):
            async for _ in phase.execute(mock_ctx, ""):
                pass

    @pytest.mark.asyncio
    async def test_deep_research_missing_final_story_list(self):
        """Test DeepResearchPhase error handling for missing final story list."""
        phase = DeepResearchPhase()
        mock_ctx = MagicMock()
        mock_ctx.session.state = {}  # No final_story_list
        
        with pytest.raises(ValueError, match="No final story list found for deep research"):
            async for _ in phase.execute(mock_ctx, "user requirements"):
                pass

    @pytest.mark.asyncio
    async def test_deep_research_empty_final_story_list(self):
        """Test DeepResearchPhase error handling for empty final story list."""
        phase = DeepResearchPhase()
        mock_ctx = MagicMock()
        mock_ctx.session.state = {"final_story_list": []}  # Empty list
        
        with pytest.raises(ValueError, match="No final story list found for deep research"):
            async for _ in phase.execute(mock_ctx, "user requirements"):
                pass

    # Error Message Quality Tests
    def test_error_messages_are_clear_and_specific(self):
        """Test that all error messages are clear and specific."""
        # Test that error messages follow the pattern from documentation
        expected_patterns = [
            "No user requirements",
            "No research plan", 
            "No discovered stories",
            "No final story list",
            "Failed to identify topic areas",
            "Failed to validate stories"
        ]
        
        # This test verifies that our error messages are descriptive
        # The actual error message testing is done in the individual test methods above
        assert len(expected_patterns) > 0  # Ensure we have defined error patterns

    def test_fail_fast_behavior(self):
        """Test that components fail fast rather than using silent fallbacks."""
        # Verify that phases don't have silent fallback mechanisms
        discovery_phase = StoryDiscoveryPhase()
        validation_phase = StoryValidationPhase()
        research_phase = DeepResearchPhase()
        
        # Should not have fallback methods that hide errors
        assert not hasattr(discovery_phase, '_fallback_topic_areas')
        assert not hasattr(validation_phase, '_fallback_validation')
        assert not hasattr(research_phase, '_fallback_research')
        
        # Should not have default values that mask missing inputs
        # (This is verified by the explicit error handling tests above)

    @pytest.mark.asyncio
    async def test_error_propagation_through_workflow(self):
        """Test that errors propagate correctly through the complete workflow."""
        coordinator = DynamicResearchCoordinator()
        
        # Test error propagation from Phase 1
        mock_ctx = MagicMock()
        mock_ctx.session.state = {
            "user_requirements": "",  # Invalid input
            "research_plan": "valid plan"
        }
        
        # Error should propagate from coordinator validation
        with pytest.raises(ValueError, match="No user requirements found for research coordination"):
            async for _ in coordinator._run_async_impl(mock_ctx):
                pass

    def test_error_handling_follows_documentation_principles(self):
        """Test that error handling follows principles from ANTI_PATTERNS_AND_VALIDATION.md."""
        # Verify explicit error handling (no silent failures)
        coordinator = DynamicResearchCoordinator()
        
        # Should not have methods that return None or empty values on error
        assert not hasattr(coordinator, '_safe_get_requirements')
        assert not hasattr(coordinator, '_try_get_research_plan')
        
        # Should use ValueError for missing inputs (as documented)
        # This is verified by all the error handling tests above
        
        # Should have clear error messages (not generic ones)
        # This is verified by the specific error message patterns tested above
