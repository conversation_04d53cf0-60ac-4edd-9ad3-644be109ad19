"""Unit tests for the LoggedSessionService."""

import json
import tempfile
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from underlines_adk.logging.json_file_logger import LOG_DIRECTORY
from underlines_adk.sessions import LoggedSessionService


@pytest.fixture
def temp_log_dir(monkeypatch):
    """Fixture to create a temporary log directory for tests."""
    with tempfile.TemporaryDirectory() as temp_dir:
        monkeypatch.setattr(
            "underlines_adk.logging.json_file_logger.LOG_DIRECTORY",
            temp_dir,
        )
        yield Path(temp_dir)


@pytest.fixture
def mock_event():
    """Fixture to create a mock ADK Event for testing."""
    event = MagicMock()
    event.id = "evt_123"
    event.author = "test_agent"
    event.invocation_id = "inv_456"
    event.is_final_response.return_value = True
    event.turn_complete = True
    event.error_code = None
    event.error_message = None
    event.content = None
    event.actions = None
    return event


@pytest.fixture
def mock_session():
    """Fixture to create a mock ADK Session for testing."""
    session = MagicMock()
    session.id = "ses_789"
    return session


@pytest.fixture
def mock_underlying_service():
    """Fixture to create a mock underlying BaseSessionService."""
    return AsyncMock()


@pytest.mark.asyncio
async def test_append_event_logs_and_delegates(
    temp_log_dir, mock_underlying_service, mock_session, mock_event
):
    """
    Verify `append_event` logs the event and then calls the underlying service.
    """
    # Arrange
    service = LoggedSessionService(underlying_service=mock_underlying_service)

    # Act
    await service.append_event(mock_session, mock_event)

    # Assert
    # 1. Check that the event was logged
    log_file = temp_log_dir / f"{mock_session.id}.jsonl"
    assert log_file.exists(), "Log file was not created."
    with open(log_file, "r") as f:
        data = json.load(f)
    assert data["event_id"] == mock_event.id

    # 2. Check that the call was delegated to the underlying service
    mock_underlying_service.append_event.assert_awaited_once_with(
        mock_session, mock_event
    )


@pytest.mark.asyncio
async def test_create_session_delegates(mock_underlying_service):
    """Verify `create_session` delegates its call."""
    service = LoggedSessionService(underlying_service=mock_underlying_service)
    await service.create_session(app_name="test")
    mock_underlying_service.create_session.assert_awaited_once_with(app_name="test")


@pytest.mark.asyncio
async def test_get_session_delegates(mock_underlying_service):
    """Verify `get_session` delegates its call."""
    service = LoggedSessionService(underlying_service=mock_underlying_service)
    await service.get_session(session_id="123")
    mock_underlying_service.get_session.assert_awaited_once_with(session_id="123")


@pytest.mark.asyncio
async def test_list_sessions_delegates(mock_underlying_service):
    """Verify `list_sessions` delegates its call."""
    service = LoggedSessionService(underlying_service=mock_underlying_service)
    await service.list_sessions(user_id="user1")
    mock_underlying_service.list_sessions.assert_awaited_once_with(user_id="user1")


@pytest.mark.asyncio
async def test_delete_session_delegates(mock_underlying_service):
    """Verify `delete_session` delegates its call."""
    service = LoggedSessionService(underlying_service=mock_underlying_service)
    await service.delete_session(session_id="123")
    mock_underlying_service.delete_session.assert_awaited_once_with(session_id="123")


def test_uses_in_memory_service_by_default():
    """Verify it defaults to InMemorySessionService if none is provided."""
    from google.adk.sessions import InMemorySessionService

    service = LoggedSessionService()
    assert isinstance(service._service, InMemorySessionService)
