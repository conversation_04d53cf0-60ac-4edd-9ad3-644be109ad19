"""
Unit tests for DeepResearchPhase.

Tests the Phase 3 implementation of the story-centric workflow:
- Per-story deep research with LoopAgent coordination
- DeepSearchAgent + QualityAssessmentAgent per story
- Parallel story research execution
- Session state management with PRD-defined keys
- Error handling and validation
"""

import pytest
from unittest.mock import MagicMock, AsyncMock, patch

from underlines_adk.agents.phases.deep_research_phase import DeepResearchPhase
from google.adk.events import Event, EventActions


class TestDeepResearchPhase:
    """Test suite for DeepResearchPhase implementation."""

    def test_phase_initialization(self):
        """Test that the phase initializes correctly."""
        phase = DeepResearchPhase()
        assert phase.coordinator_name == "DynamicResearchCoordinator"
        
        # Test with custom coordinator name
        custom_phase = DeepResearchPhase(coordinator_name="CustomCoordinator")
        assert custom_phase.coordinator_name == "CustomCoordinator"

    @pytest.mark.asyncio
    async def test_execute_raises_error_for_missing_user_requirements(self):
        """Test that execute raises ValueError when user requirements are missing."""
        phase = DeepResearchPhase()
        mock_ctx = MagicMock()
        
        # Should raise error for empty user requirements
        with pytest.raises(ValueError, match="No user requirements provided for deep research"):
            async for _ in phase.execute(mock_ctx, ""):
                pass

    @pytest.mark.asyncio
    async def test_execute_raises_error_for_missing_final_story_list(self):
        """Test that execute raises ValueError when final story list is missing."""
        phase = DeepResearchPhase()
        mock_ctx = MagicMock()
        mock_ctx.session.state = {}  # No final_story_list
        
        # Should raise error for missing final story list
        with pytest.raises(ValueError, match="No final story list found for deep research"):
            async for _ in phase.execute(mock_ctx, "Some requirements"):
                pass

    @pytest.mark.asyncio
    @patch('underlines_adk.agents.phases.deep_research_phase.ParallelAgent')
    async def test_execute_successful_workflow(self, mock_parallel_agent):
        """Test successful execution of the deep research workflow."""
        phase = DeepResearchPhase()
        mock_ctx = MagicMock()
        
        # Mock final story list in session state
        final_story_list = [
            {"id": "story1", "title": "AI breakthrough", "summary": "Major AI development"},
            {"id": "story2", "title": "Tech funding", "summary": "New funding round"}
        ]
        mock_ctx.session.state = {"final_story_list": final_story_list}
        
        # Mock ParallelAgent execution
        mock_parallel_instance = MagicMock()
        mock_parallel_agent.return_value = mock_parallel_instance
        
        async def mock_parallel_run(ctx):
            yield Event(author="ParallelAgent", actions=EventActions(state_delta={"research_results": "test"}))
        
        mock_parallel_instance.run_async = mock_parallel_run
        
        # Execute the phase
        events = []
        async for event in phase.execute(mock_ctx, "Daily tech newsletter"):
            events.append(event)
        
        # Verify we got the expected events
        assert len(events) == 2  # parallel execution + story_research_outputs
        
        # Verify story research outputs event
        final_event = events[-1]
        assert final_event.author == "DynamicResearchCoordinator"
        assert "story_research_outputs" in final_event.actions.state_delta
        
        # Verify ParallelAgent was created with correct number of sub-agents
        mock_parallel_agent.assert_called_once()
        call_args = mock_parallel_agent.call_args
        assert call_args[1]["name"] == "ParallelStoryResearch"
        assert len(call_args[1]["sub_agents"]) == 2  # One LoopAgent per story

    @patch('underlines_adk.agents.phases.deep_research_phase.QualityAssessmentAgent')
    @patch('underlines_adk.agents.phases.deep_research_phase.LoopAgent')
    def test_create_story_research_loop(self, mock_loop_agent, mock_quality_agent):
        """Test creation of LoopAgent for individual story research."""
        phase = DeepResearchPhase()
        
        # Mock story data
        story = {"id": "story1", "title": "AI breakthrough"}
        story_index = 0
        user_requirements = "Daily tech newsletter"
        
        # Mock quality agent creation
        mock_quality_instance = MagicMock()
        mock_quality_agent.return_value = mock_quality_instance
        
        # Mock LoopAgent creation
        mock_loop_instance = MagicMock()
        mock_loop_agent.return_value = mock_loop_instance
        
        # Call the method
        result = phase._create_story_research_loop(story, story_index, user_requirements)
        
        # Verify QualityAssessmentAgent was created correctly
        mock_quality_agent.assert_called_once()
        quality_call_args = mock_quality_agent.call_args
        assert quality_call_args[1]["name"] == "StoryQualityAssessor_story1"
        assert "AI breakthrough" in quality_call_args[1]["description"]
        
        # Verify LoopAgent was created correctly
        mock_loop_agent.assert_called_once()
        loop_call_args = mock_loop_agent.call_args
        assert loop_call_args[1]["name"] == "StoryResearchLoop_story1"
        assert len(loop_call_args[1]["sub_agents"]) == 2  # DeepSearchAgent + QualityAssessmentAgent
        assert "max_iterations" in loop_call_args[1]
        
        # Verify the result is the mock LoopAgent instance
        assert result == mock_loop_instance

    @patch('underlines_adk.agents.phases.deep_research_phase.QualityAssessmentAgent')
    @patch('underlines_adk.agents.phases.deep_research_phase.LoopAgent')
    def test_create_story_research_loop_with_missing_story_id(self, mock_loop_agent, mock_quality_agent):
        """Test LoopAgent creation when story doesn't have an ID."""
        phase = DeepResearchPhase()
        
        # Mock story data without ID
        story = {"title": "Some story"}
        story_index = 2
        user_requirements = "Newsletter requirements"
        
        # Mock instances
        mock_quality_agent.return_value = MagicMock()
        mock_loop_agent.return_value = MagicMock()
        
        # Call the method
        phase._create_story_research_loop(story, story_index, user_requirements)
        
        # Verify fallback ID was used
        quality_call_args = mock_quality_agent.call_args
        assert quality_call_args[1]["name"] == "StoryQualityAssessor_story_2"
        
        loop_call_args = mock_loop_agent.call_args
        assert loop_call_args[1]["name"] == "StoryResearchLoop_story_2"

    @patch('underlines_adk.agents.phases.deep_research_phase.QualityAssessmentAgent')
    @patch('underlines_adk.agents.phases.deep_research_phase.LoopAgent')
    def test_create_story_research_loop_with_missing_story_title(self, mock_loop_agent, mock_quality_agent):
        """Test LoopAgent creation when story doesn't have a title."""
        phase = DeepResearchPhase()
        
        # Mock story data without title
        story = {"id": "story1"}
        story_index = 1
        user_requirements = "Newsletter requirements"
        
        # Mock instances
        mock_quality_agent.return_value = MagicMock()
        mock_loop_agent.return_value = MagicMock()
        
        # Call the method
        phase._create_story_research_loop(story, story_index, user_requirements)
        
        # Verify fallback title was used
        quality_call_args = mock_quality_agent.call_args
        assert "Story 2" in quality_call_args[1]["description"]  # story_index + 1

    @pytest.mark.asyncio
    async def test_aggregate_story_research_placeholder(self):
        """Test that story research aggregation returns placeholder for initial implementation."""
        phase = DeepResearchPhase()
        mock_ctx = MagicMock()
        
        # Should return empty list as placeholder
        result = await phase._aggregate_story_research(mock_ctx)
        assert result == []

    def test_phase_follows_agentic_principles(self):
        """Test that the phase follows agentic principles from documentation."""
        phase = DeepResearchPhase()
        
        # Should not have hardcoded quality criteria
        assert not hasattr(phase, '_quality_thresholds')
        assert not hasattr(phase, '_research_templates')
        
        # Should use existing agents without custom wrappers
        # (verified by checking the implementation uses DeepSearchAgent and QualityAssessmentAgent directly)
        
        # Should implement per-story quality assessment (not global)
        assert hasattr(phase, '_create_story_research_loop')

    @patch('underlines_adk.agents.phases.deep_research_phase.os')
    @patch('underlines_adk.agents.phases.deep_research_phase.LoopAgent')
    def test_max_iterations_configuration(self, mock_loop_agent, mock_os):
        """Test that max_iterations is configurable via environment variables."""
        phase = DeepResearchPhase()
        
        # Mock environment variable
        mock_os.getenv.return_value = "5"
        mock_loop_agent.return_value = MagicMock()
        
        # Create a story research loop
        story = {"id": "test", "title": "Test"}
        phase._create_story_research_loop(story, 0, "requirements")
        
        # Verify environment variable was checked
        mock_os.getenv.assert_called_with("MAX_STORY_RESEARCH_ITERATIONS", "3")
        
        # Verify max_iterations was set correctly
        loop_call_args = mock_loop_agent.call_args
        assert loop_call_args[1]["max_iterations"] == 5

    def test_phase_uses_adk_native_patterns(self):
        """Test that the phase uses ADK-native patterns."""
        phase = DeepResearchPhase()
        
        # Should use ParallelAgent for concurrent story research
        # (verified by checking imports and usage in execute method)
        
        # Should use LoopAgent for per-story iterative research
        # (verified by checking _create_story_research_loop method)
        
        # Should use existing agents directly (no custom wrappers)
        # (verified by checking imports of deep_search_agent and QualityAssessmentAgent)
        
        # This test verifies the architectural patterns are followed
        assert hasattr(phase, 'execute')
        assert hasattr(phase, '_create_story_research_loop')
        assert hasattr(phase, '_aggregate_story_research')

    @pytest.mark.asyncio
    @patch('underlines_adk.agents.phases.deep_research_phase.ParallelAgent')
    async def test_session_state_management(self, mock_parallel_agent):
        """Test proper session state management with PRD-defined keys."""
        phase = DeepResearchPhase()
        mock_ctx = MagicMock()
        
        # Mock final story list in session state (PRD-defined key)
        final_story_list = [{"id": "story1", "title": "Test story"}]
        mock_ctx.session.state = {"final_story_list": final_story_list}
        
        # Mock ParallelAgent execution
        mock_parallel_instance = MagicMock()
        mock_parallel_agent.return_value = mock_parallel_instance
        
        async def mock_parallel_run(ctx):
            yield Event(author="ParallelAgent", actions=EventActions(state_delta={}))
        
        mock_parallel_instance.run_async = mock_parallel_run
        
        # Execute the phase
        events = []
        async for event in phase.execute(mock_ctx, "requirements"):
            events.append(event)
        
        # Verify session state key usage
        # Phase reads from "final_story_list" (PRD-defined)
        mock_ctx.session.state.get.assert_called_with("final_story_list", [])
        
        # Phase saves to "story_research_outputs" (PRD-defined)
        final_event = events[-1]
        assert "story_research_outputs" in final_event.actions.state_delta
