"""
Unit tests for StoryDiscoveryPhase.

Tests the Phase 1 implementation of the story-centric workflow:
- Topic analysis and identification using LLM intelligence
- Parallel WideSearchAgent coordination
- Story aggregation and session state management
- Error handling and validation
"""

import pytest
from unittest.mock import MagicMock, AsyncMock, patch

from underlines_adk.agents.phases.story_discovery_phase import StoryDiscoveryPhase
from google.adk.events import Event, EventActions


class TestStoryDiscoveryPhase:
    """Test suite for StoryDiscoveryPhase implementation."""

    def test_phase_initialization(self):
        """Test that the phase initializes correctly."""
        phase = StoryDiscoveryPhase()
        assert phase.coordinator_name == "DynamicResearchCoordinator"
        
        # Test with custom coordinator name
        custom_phase = StoryDiscoveryPhase(coordinator_name="CustomCoordinator")
        assert custom_phase.coordinator_name == "CustomCoordinator"

    @pytest.mark.asyncio
    async def test_execute_raises_error_for_missing_user_requirements(self):
        """Test that execute raises ValueError when user requirements are missing."""
        phase = StoryDiscoveryPhase()
        mock_ctx = MagicMock()
        mock_ctx.session.state = {"user_requirements": "", "research_plan": "some research plan"}

        # Should raise error for empty user requirements
        with pytest.raises(ValueError, match="No user requirements provided for story discovery"):
            async for _ in phase.execute(mock_ctx):
                pass

    @pytest.mark.asyncio
    async def test_execute_raises_error_for_missing_research_plan(self):
        """Test that execute raises ValueError when research plan is missing."""
        phase = StoryDiscoveryPhase()
        mock_ctx = MagicMock()
        
        # Should raise error for empty research plan
        with pytest.raises(ValueError, match="No research plan provided for story discovery"):
            async for _ in phase.execute(mock_ctx, "some requirements", ""):
                pass

    @pytest.mark.asyncio
    @patch('underlines_adk.agents.phases.story_discovery_phase.llm')
    @patch('underlines_adk.agents.phases.story_discovery_phase.wide_search_agent')
    @patch('underlines_adk.agents.phases.story_discovery_phase.ParallelAgent')
    async def test_execute_successful_workflow(self, mock_parallel_agent, mock_wide_search_agent, mock_llm):
        """Test successful execution of the story discovery workflow."""
        phase = StoryDiscoveryPhase()
        mock_ctx = MagicMock()
        
        # Mock LLM response for topic identification
        mock_llm_response = MagicMock()
        mock_llm_response.generations = [[MagicMock()]]
        mock_llm_response.generations[0][0].text = "AI developments\nTech startups\nMarket analysis"
        mock_llm.agenerate = AsyncMock(return_value=mock_llm_response)
        
        # Mock ParallelAgent execution
        mock_parallel_instance = MagicMock()
        mock_parallel_agent.return_value = mock_parallel_instance
        
        async def mock_parallel_run(ctx):
            yield Event(author="ParallelAgent", actions=EventActions(state_delta={"parallel_results": "test"}))
        
        mock_parallel_instance.run_async = mock_parallel_run
        
        # Execute the phase
        events = []
        async for event in phase.execute(mock_ctx, "Daily tech newsletter", "Research tech trends"):
            events.append(event)
        
        # Verify we got the expected events
        assert len(events) == 3  # topic_areas, parallel execution, discovered_stories
        
        # Verify topic areas event
        topic_event = events[0]
        assert topic_event.author == "DynamicResearchCoordinator"
        assert "topic_areas" in topic_event.actions.state_delta
        assert len(topic_event.actions.state_delta["topic_areas"]) == 3
        
        # Verify LLM was called correctly
        mock_llm.agenerate.assert_called_once()
        call_args = mock_llm.agenerate.call_args
        assert "Daily tech newsletter" in call_args[1]["messages"][0]["content"]
        assert "Research tech trends" in call_args[1]["messages"][0]["content"]

    @pytest.mark.asyncio
    @patch('underlines_adk.agents.phases.story_discovery_phase.llm')
    async def test_identify_topic_areas_success(self, mock_llm):
        """Test successful topic area identification."""
        phase = StoryDiscoveryPhase()
        
        # Mock LLM response
        mock_llm_response = MagicMock()
        mock_llm_response.generations = [[MagicMock()]]
        mock_llm_response.generations[0][0].text = "AI developments\nTech startups\nMarket analysis\n\n"
        mock_llm.agenerate = AsyncMock(return_value=mock_llm_response)
        
        # Call the method
        topic_areas = await phase._identify_topic_areas("Daily tech newsletter", "Research tech trends")
        
        # Verify results
        assert len(topic_areas) == 3
        assert "AI developments" in topic_areas
        assert "Tech startups" in topic_areas
        assert "Market analysis" in topic_areas

    @pytest.mark.asyncio
    @patch('underlines_adk.agents.phases.story_discovery_phase.llm')
    async def test_identify_topic_areas_handles_empty_response(self, mock_llm):
        """Test that empty LLM response raises appropriate error."""
        phase = StoryDiscoveryPhase()
        
        # Mock empty LLM response
        mock_llm_response = MagicMock()
        mock_llm_response.generations = [[MagicMock()]]
        mock_llm_response.generations[0][0].text = "   \n\n   "  # Only whitespace
        mock_llm.agenerate = AsyncMock(return_value=mock_llm_response)
        
        # Should raise error for no topic areas
        with pytest.raises(ValueError, match="LLM failed to identify any topic areas"):
            await phase._identify_topic_areas("Some requirements", "Some plan")

    @pytest.mark.asyncio
    @patch('underlines_adk.agents.phases.story_discovery_phase.llm')
    async def test_identify_topic_areas_handles_llm_failure(self, mock_llm):
        """Test that LLM failures are handled with clear error messages."""
        phase = StoryDiscoveryPhase()
        
        # Mock LLM failure
        mock_llm.agenerate = AsyncMock(side_effect=Exception("API timeout"))
        
        # Should raise error with clear message
        with pytest.raises(ValueError, match="Failed to identify topic areas: API timeout"):
            await phase._identify_topic_areas("Some requirements", "Some plan")

    @pytest.mark.asyncio
    async def test_aggregate_stories_placeholder(self):
        """Test that story aggregation returns placeholder for initial implementation."""
        phase = StoryDiscoveryPhase()
        mock_ctx = MagicMock()
        
        # Should return empty list as placeholder
        result = await phase._aggregate_stories(mock_ctx)
        assert result == []

    def test_phase_follows_agentic_principles(self):
        """Test that the phase follows agentic principles from documentation."""
        phase = StoryDiscoveryPhase()
        
        # Should not have hardcoded topic areas
        assert not hasattr(phase, '_hardcoded_topics')
        assert not hasattr(phase, '_newsletter_types')
        
        # Should use LLM intelligence for topic identification
        assert hasattr(phase, '_identify_topic_areas')
        
        # Should use existing agents without custom wrappers
        # (verified by checking imports in the actual implementation)

    @pytest.mark.asyncio
    @patch('underlines_adk.agents.phases.story_discovery_phase.llm')
    async def test_topic_identification_prompt_structure(self, mock_llm):
        """Test that the topic identification prompt is properly structured."""
        phase = StoryDiscoveryPhase()
        
        # Mock LLM response
        mock_llm_response = MagicMock()
        mock_llm_response.generations = [[MagicMock()]]
        mock_llm_response.generations[0][0].text = "Topic 1\nTopic 2"
        mock_llm.agenerate = AsyncMock(return_value=mock_llm_response)
        
        # Call with specific requirements
        await phase._identify_topic_areas("Biotech VC newsletter focusing on TL1A", "Deep research plan")
        
        # Verify prompt structure
        call_args = mock_llm.agenerate.call_args
        prompt = call_args[1]["messages"][0]["content"]
        
        # Should include user requirements and research plan
        assert "Biotech VC newsletter focusing on TL1A" in prompt
        assert "Deep research plan" in prompt
        
        # Should provide clear instructions
        assert "3-5 distinct topic areas" in prompt
        assert "RESPONSE FORMAT" in prompt
        
        # Should include examples for guidance
        assert "Example for a biotech VC newsletter" in prompt
        assert "Example for a daily tech newsletter" in prompt
