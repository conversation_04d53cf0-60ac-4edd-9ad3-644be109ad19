"""
Unit tests for DynamicResearchCoordinator agent.

Tests the modular DynamicResearchCoordinator agent that orchestrates the
story-centric workflow through separate phase implementations. Validates
that it follows agentic principles and maintains the refactored architecture.
"""

import pytest
from unittest.mock import MagicMock, AsyncMock, patch

from underlines_adk.agents.dynamic_research_coordinator import DynamicResearchCoordinator


class TestDynamicResearchCoordinator:
    """Test suite for DynamicResearchCoordinator modular architecture and agentic principles."""

    def test_coordinator_has_correct_configuration(self):
        """Test that the coordinator is configured correctly with modular architecture."""
        coordinator = DynamicResearchCoordinator()

        assert coordinator.name == "DynamicResearchCoordinator"
        assert "modular phase implementations" in coordinator.description
        assert coordinator.sub_agents == []  # Phases handle their own agent coordination

    def test_coordinator_with_custom_name_and_description(self):
        """Test coordinator with custom configuration."""
        custom_coordinator = DynamicResearchCoordinator(
            name="CustomCoordinator",
            description="Custom description for testing"
        )

        assert custom_coordinator.name == "CustomCoordinator"
        assert custom_coordinator.description == "Custom description for testing"

    def test_coordinator_initializes_phase_instances(self):
        """Test that the coordinator initializes all phase instances correctly."""
        coordinator = DynamicResearchCoordinator()

        # Should have all three phase instances
        assert hasattr(coordinator, 'story_discovery_phase')
        assert hasattr(coordinator, 'story_validation_phase')
        assert hasattr(coordinator, 'deep_research_phase')

        # Phase instances should be configured with coordinator name
        assert coordinator.story_discovery_phase.coordinator_name == "DynamicResearchCoordinator"
        assert coordinator.story_validation_phase.coordinator_name == "DynamicResearchCoordinator"
        assert coordinator.deep_research_phase.coordinator_name == "DynamicResearchCoordinator"

    def test_coordinator_phase_instances_with_custom_name(self):
        """Test that phase instances use custom coordinator name."""
        coordinator = DynamicResearchCoordinator(name="CustomCoordinator")

        # All phases should use the custom coordinator name
        assert coordinator.story_discovery_phase.coordinator_name == "CustomCoordinator"
        assert coordinator.story_validation_phase.coordinator_name == "CustomCoordinator"
        assert coordinator.deep_research_phase.coordinator_name == "CustomCoordinator"

    def test_coordinator_follows_agentic_principles(self):
        """Test that the coordinator follows agentic principles through modular phases."""
        coordinator = DynamicResearchCoordinator()

        # Check that the coordinator doesn't have hardcoded programmatic logic
        # The modular version delegates to phase classes instead of having complex methods
        assert not hasattr(coordinator, '_determine_research_areas_needed')
        assert not hasattr(coordinator, '_create_research_workflows_from_requirements')
        assert not hasattr(coordinator, '_story_discovery_phase')  # Now handled by phase class
        assert not hasattr(coordinator, '_story_validation_phase')  # Now handled by phase class

        # Check that it follows modular architecture patterns
        assert hasattr(coordinator, '_run_async_impl')
        assert hasattr(coordinator, 'story_discovery_phase')
        assert hasattr(coordinator, 'story_validation_phase')
        assert hasattr(coordinator, 'deep_research_phase')

    def test_modular_architecture_patterns(self):
        """Test that the coordinator uses modular architecture patterns."""
        coordinator = DynamicResearchCoordinator()

        # Verify the core async implementation method exists and is callable
        assert hasattr(coordinator, '_run_async_impl')
        assert callable(getattr(coordinator, '_run_async_impl'))

        # Verify phase instances are properly initialized
        from underlines_adk.agents.phases.story_discovery_phase import StoryDiscoveryPhase
        from underlines_adk.agents.phases.story_validation_phase import StoryValidationPhase
        from underlines_adk.agents.phases.deep_research_phase import DeepResearchPhase

        assert isinstance(coordinator.story_discovery_phase, StoryDiscoveryPhase)
        assert isinstance(coordinator.story_validation_phase, StoryValidationPhase)
        assert isinstance(coordinator.deep_research_phase, DeepResearchPhase)

    def test_coordinator_universal_flexibility_design(self):
        """Test that the coordinator maintains universal flexibility through modular phases."""
        coordinator = DynamicResearchCoordinator()

        # The coordinator should delegate to phase classes for flexibility
        # No hardcoded newsletter-specific logic in the coordinator itself
        assert hasattr(coordinator, 'story_discovery_phase')
        assert hasattr(coordinator, 'story_validation_phase')
        assert hasattr(coordinator, 'deep_research_phase')

        # The coordinator should be simple and focused on orchestration
        # Complex logic should be in phase classes
        coordinator_methods = [name for name in dir(coordinator)
                             if not name.startswith('_') and callable(getattr(coordinator, name))]

        # Should have minimal public methods (just initialization handled by BaseAgent)
        # Main logic is in _run_async_impl and phase instances
        assert len(coordinator_methods) <= 2  # Minimal public interface

    def test_coordinator_description_emphasizes_modular_architecture(self):
        """Test that the coordinator description emphasizes modular architecture."""
        coordinator = DynamicResearchCoordinator()
        description = coordinator.description.lower()

        # Should emphasize modular architecture
        assert "modular" in description or "phase" in description

        # Should not emphasize programmatic approaches
        assert "keyword" not in description
        assert "hardcoded" not in description or "no hardcoded" in description

    def test_coordinator_follows_documentation_principles(self):
        """Test that the coordinator follows the principles from our documentation."""
        coordinator = DynamicResearchCoordinator()

        # Should be a BaseAgent (for custom orchestration)
        from google.adk.agents import BaseAgent
        assert isinstance(coordinator, BaseAgent)

        # Should not have hardcoded sub-agents (phases handle their own coordination)
        assert coordinator.sub_agents == []

        # Should have the core async implementation method
        assert hasattr(coordinator, '_run_async_impl')

        # Should have modular phase architecture
        assert hasattr(coordinator, 'story_discovery_phase')
        assert hasattr(coordinator, 'story_validation_phase')
        assert hasattr(coordinator, 'deep_research_phase')

    @pytest.mark.asyncio
    async def test_coordinator_raises_error_for_missing_user_requirements(self):
        """Test that the coordinator raises proper errors when user requirements are missing."""
        coordinator = DynamicResearchCoordinator()

        # Mock context with missing user requirements
        mock_ctx = MagicMock()
        mock_ctx.session.state = {
            "research_plan": "Some research plan"
            # Missing "user_requirements"
        }

        # Should raise ValueError for missing user requirements
        with pytest.raises(ValueError, match="No user requirements found for research coordination"):
            async for _ in coordinator._run_async_impl(mock_ctx):
                pass

    @pytest.mark.asyncio
    async def test_coordinator_raises_error_for_missing_research_plan(self):
        """Test that the coordinator raises proper errors when research plan is missing."""
        coordinator = DynamicResearchCoordinator()

        # Mock context with missing research plan
        mock_ctx = MagicMock()
        mock_ctx.session.state = {
            "user_requirements": "Some user requirements"
            # Missing "research_plan"
        }

        # Should raise ValueError for missing research plan
        with pytest.raises(ValueError, match="No research plan found for research coordination"):
            async for _ in coordinator._run_async_impl(mock_ctx):
                pass

    @pytest.mark.asyncio
    async def test_coordinator_raises_error_for_empty_requirements(self):
        """Test that the coordinator raises proper errors when requirements are empty strings."""
        coordinator = DynamicResearchCoordinator()

        # Mock context with empty user requirements
        mock_ctx = MagicMock()
        mock_ctx.session.state = {
            "user_requirements": "",  # Empty string should trigger error
            "research_plan": "Some research plan"
        }

        # Should raise ValueError for empty user requirements
        with pytest.raises(ValueError, match="No user requirements found for research coordination"):
            async for _ in coordinator._run_async_impl(mock_ctx):
                pass

    @pytest.mark.asyncio
    @patch('underlines_adk.agents.dynamic_research_coordinator.StoryDiscoveryPhase')
    @patch('underlines_adk.agents.dynamic_research_coordinator.StoryValidationPhase')
    @patch('underlines_adk.agents.dynamic_research_coordinator.DeepResearchPhase')
    async def test_coordinator_executes_all_phases_in_sequence(self, mock_deep_phase_class,
                                                               mock_validation_phase_class,
                                                               mock_discovery_phase_class):
        """Test that the coordinator executes all phases in the correct sequence."""
        coordinator = DynamicResearchCoordinator()

        # Mock phase instances
        mock_discovery_phase = AsyncMock()
        mock_validation_phase = AsyncMock()
        mock_deep_phase = AsyncMock()

        mock_discovery_phase_class.return_value = mock_discovery_phase
        mock_validation_phase_class.return_value = mock_validation_phase
        mock_deep_phase_class.return_value = mock_deep_phase

        # Mock phase execution to yield events
        async def mock_phase_execution(*args):
            yield MagicMock()

        mock_discovery_phase.execute = mock_phase_execution
        mock_validation_phase.execute = mock_phase_execution
        mock_deep_phase.execute = mock_phase_execution

        # Mock context with valid state
        mock_ctx = MagicMock()
        mock_ctx.session.state = {
            "user_requirements": "Daily tech newsletter",
            "research_plan": "Research tech trends"
        }

        # Execute coordinator
        events = []
        async for event in coordinator._run_async_impl(mock_ctx):
            events.append(event)

        # Verify all phases were executed in sequence
        mock_discovery_phase.execute.assert_called_once_with(
            mock_ctx, "Daily tech newsletter", "Research tech trends"
        )
        mock_validation_phase.execute.assert_called_once_with(
            mock_ctx, "Daily tech newsletter"
        )
        mock_deep_phase.execute.assert_called_once_with(
            mock_ctx, "Daily tech newsletter"
        )

        # Verify we got events from all phases
        assert len(events) == 3  # One event from each phase

    def test_coordinator_anti_patterns_avoided(self):
        """Test that the coordinator avoids anti-patterns from documentation."""
        coordinator = DynamicResearchCoordinator()

        # Should not have hardcoded dictionary return structures
        assert not hasattr(coordinator, '_create_hardcoded_response')

        # Should not have newsletter-specific agent creation
        assert not hasattr(coordinator, '_create_daily_news_agent')
        assert not hasattr(coordinator, '_create_biotech_agent')

        # Should not have programmatic quality assessment
        assert not hasattr(coordinator, '_calculate_quality_score')
        assert not hasattr(coordinator, '_assess_quality_programmatically')

        # Should use modular phase architecture instead
        assert hasattr(coordinator, 'story_discovery_phase')
        assert hasattr(coordinator, 'story_validation_phase')
        assert hasattr(coordinator, 'deep_research_phase')
