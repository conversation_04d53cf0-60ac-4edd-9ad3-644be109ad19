# Newsletter Generation Pipeline - Temporal Flow

This sequence diagram shows the interactions and timing of the multi-stage coordination process.

```mermaid
sequenceDiagram
    participant U as User
    participant RP as ResearchPlanner
    participant DRC as DynamicResearchCoordinator
    participant SS as Session State
    participant PA1 as ParallelAgent<br/>(Topic Discovery)
    participant WS1 as WideSearchAgent<br/>(Topic 1)
    participant WS2 as WideSearchAgent<br/>(Topic 2)
    participant WSN as WideSearchAgent<br/>(Topic N)
    participant PA2 as ParallelAgent<br/>(Story Research)
    participant LA1 as LoopAgent<br/>(Story 1)
    participant LA2 as LoopAgent<br/>(Story 2)
    participant LAN as LoopAgent<br/>(Story N)
    participant DS as DeepSearchAgent
    participant QA as QualityAssessmentAgent
    participant FNS as FlexibleNewsletterSynthesizer

    %% Phase 0: Initial Planning
    U->>RP: Natural Language Newsletter Requirements
    RP->>SS: Save research_plan
    RP->>DRC: Trigger coordination

    %% Phase 1: Topic Analysis & Story Discovery
    Note over DRC: Phase 1: Story Discovery
    DRC->>SS: Read user_requirements & research_plan
    DRC->>DRC: Analyze requirements → identify topic areas
    DRC->>SS: Save topic_areas
    
    DRC->>PA1: Create ParallelAgent with WideSearchAgents
    PA1->>WS1: Search Topic Area 1
    PA1->>WS2: Search Topic Area 2
    PA1->>WSN: Search Topic Area N
    
    par Parallel Topic Discovery
        WS1->>WS1: Discover stories for Topic 1
        WS2->>WS2: Discover stories for Topic 2
        WSN->>WSN: Discover stories for Topic N
    end
    
    WS1->>PA1: Return Topic 1 stories
    WS2->>PA1: Return Topic 2 stories
    WSN->>PA1: Return Topic N stories
    PA1->>DRC: Aggregated story discoveries
    DRC->>SS: Save discovered_stories

    %% Phase 2: Story Validation & Regrouping
    Note over DRC: Phase 2: Story Validation
    DRC->>DRC: Validate stories against user requirements
    
    alt Stories insufficient
        DRC->>DRC: Generate corrective instructions
        DRC->>PA1: Reassign WideSearchAgents with corrections
        Note over PA1,WSN: Repeat discovery with corrections
    else Stories sufficient
        DRC->>DRC: Finalize story list
        DRC->>SS: Save final_story_list
    end

    %% Phase 3: Deep Research Per Story
    Note over DRC: Phase 3: Per-Story Deep Research
    DRC->>PA2: Create ParallelAgent with LoopAgents (one per story)
    PA2->>LA1: Create LoopAgent for Story 1
    PA2->>LA2: Create LoopAgent for Story 2
    PA2->>LAN: Create LoopAgent for Story N
    
    par Parallel Story Research
        loop Until Story 1 Quality Sufficient
            LA1->>DS: Deep research Story 1
            DS->>LA1: Research results
            LA1->>QA: Assess Story 1 quality
            QA->>LA1: Quality assessment
            alt Quality insufficient
                Note over LA1: Continue loop iteration
            else Quality sufficient
                LA1->>LA1: Escalate (exit loop)
            end
        end
        
        loop Until Story 2 Quality Sufficient
            LA2->>DS: Deep research Story 2
            DS->>LA2: Research results
            LA2->>QA: Assess Story 2 quality
            QA->>LA2: Quality assessment
            alt Quality insufficient
                Note over LA2: Continue loop iteration
            else Quality sufficient
                LA2->>LA2: Escalate (exit loop)
            end
        end
        
        loop Until Story N Quality Sufficient
            LAN->>DS: Deep research Story N
            DS->>LAN: Research results
            LAN->>QA: Assess Story N quality
            QA->>LAN: Quality assessment
            alt Quality insufficient
                Note over LAN: Continue loop iteration
            else Quality sufficient
                LAN->>LAN: Escalate (exit loop)
            end
        end
    end
    
    LA1->>PA2: Story 1 research complete
    LA2->>PA2: Story 2 research complete
    LAN->>PA2: Story N research complete
    PA2->>DRC: All story research complete
    DRC->>SS: Save story_research_outputs

    %% Phase 4: Newsletter Synthesis (Separate from DRC)
    Note over FNS: Phase 4: Newsletter Synthesis
    DRC->>FNS: Trigger synthesis with completed research
    FNS->>SS: Read story_research_outputs & user_requirements
    FNS->>FNS: Synthesize newsletter format
    FNS->>SS: Save final_newsletter
    FNS->>U: Return completed newsletter
```

## Key Temporal Insights

1. **Sequential Phases**: Each phase completes before the next begins
2. **Parallel Execution**: Multiple agents run concurrently within each phase
3. **Quality Assessment Timing**: Happens per story during deep research, not after all research completes
4. **Clear Handoffs**: DynamicResearchCoordinator completes all research, then hands off to FlexibleNewsletterSynthesizer
