# Newsletter Generation Pipeline - Workflow Overview

This flowchart shows the complete workflow structure and agent relationships in the story-centric newsletter generation pipeline.

```mermaid
graph TD
    A[User Input: Natural Language Newsletter Requirements] --> B[ResearchPlanner]
    
    B --> |research_plan| C[DynamicResearchCoordinator]
    
    %% Story Discovery Phase
    C --> D[Topic Area Analysis]
    D --> E[ParallelAgent: Multiple WideSearchAgents]
    E --> F1[WideSearchAgent: Topic Area 1]
    E --> F2[WideSearchAgent: Topic Area 2]
    E --> F3[WideSearchAgent: Topic Area N]
    
    F1 --> G[Story Discovery Aggregation]
    F2 --> G
    F3 --> G
    
    %% Story Validation & Regrouping
    G --> H[Story Validation & Regrouping]
    H --> I{Stories Meet Requirements?}
    I -->|No| J[Reassign WideSearchAgents with Corrective Instructions]
    J --> E
    I -->|Yes| K[Final Story List]
    
    %% Deep Research Phase
    K --> L[ParallelAgent: LoopAgents per Story]
    L --> M1[LoopAgent: Story 1 Research]
    L --> M2[LoopAgent: Story 2 Research]
    L --> M3[LoopAgent: Story N Research]
    
    %% Each LoopAgent contains DeepSearchAgent + QualityAssessmentAgent
    M1 --> N1[DeepSearchAgent + QualityAssessmentAgent Loop]
    M2 --> N2[DeepSearchAgent + QualityAssessmentAgent Loop]
    M3 --> N3[DeepSearchAgent + QualityAssessmentAgent Loop]
    
    N1 --> O1[Story 1 Research Complete]
    N2 --> O2[Story 2 Research Complete]
    N3 --> O3[Story N Research Complete]
    
    %% Newsletter Synthesis
    O1 --> P[All Story Research Aggregation]
    O2 --> P
    O3 --> P
    
    P --> Q[FlexibleNewsletterSynthesizer]
    Q --> R[Final Newsletter Output]
    
    %% Session State Flow
    A -.-> S1[Session State: user_requirements]
    B -.-> S2[Session State: research_plan]
    D -.-> S3[Session State: topic_areas]
    G -.-> S4[Session State: discovered_stories]
    K -.-> S5[Session State: final_story_list]
    P -.-> S6[Session State: story_research_outputs]
    Q -.-> S7[Session State: final_newsletter]
    
    %% Styling
    classDef userInput fill:#e1f5fe
    classDef orchestrator fill:#f3e5f5
    classDef agent fill:#e8f5e8
    classDef synthesizer fill:#fff3e0
    classDef output fill:#ffebee
    classDef sessionState fill:#f5f5f5,stroke-dasharray: 5 5
    
    class A userInput
    class B,C orchestrator
    class Q synthesizer
    class F1,F2,F3,N1,N2,N3 agent
    class R output
    class S1,S2,S3,S4,S5,S6,S7 sessionState
```

## Key Workflow Phases

1. **Story Discovery Phase**: DynamicResearchCoordinator identifies topic areas and spawns parallel WideSearchAgents
2. **Story Validation & Regrouping**: Validates discovered stories against user requirements with iteration capability
3. **Deep Research Phase**: Creates LoopAgents per story, each containing DeepSearchAgent + QualityAssessmentAgent
4. **Newsletter Synthesis**: FlexibleNewsletterSynthesizer formats final newsletter from completed story research
