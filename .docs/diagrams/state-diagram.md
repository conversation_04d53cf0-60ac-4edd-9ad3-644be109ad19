# Newsletter Generation Pipeline - Session State Transitions

This state diagram shows the workflow phases and session state evolution throughout the pipeline.

```mermaid
stateDiagram-v2
    [*] --> UserInput: User provides newsletter requirements
    
    UserInput --> ResearchPlanning: user_requirements saved
    state ResearchPlanning {
        [*] --> AnalyzeRequirements
        AnalyzeRequirements --> CreatePlan
        CreatePlan --> [*]: research_plan saved
    }
    
    ResearchPlanning --> StoryDiscovery: DynamicResearchCoordinator triggered
    
    state StoryDiscovery {
        [*] --> TopicAnalysis
        TopicAnalysis --> ParallelTopicSearch: topic_areas identified
        
        state ParallelTopicSearch {
            [*] --> WideSearch1
            [*] --> WideSearch2
            [*] --> WideSearchN
            WideSearch1 --> StoryAggregation
            WideSearch2 --> StoryAggregation
            WideSearchN --> StoryAggregation
        }
        
        ParallelTopicSearch --> StoryValidation: discovered_stories saved
        
        state StoryValidation {
            [*] --> ValidateStories
            ValidateStories --> StoriesSufficient: Check against requirements
            StoriesSufficient --> FinalizeStories: Yes
            StoriesSufficient --> CorrectiveInstructions: No
            CorrectiveInstructions --> ParallelTopicSearch: Reassign agents
            FinalizeStories --> [*]: final_story_list saved
        }
        
        StoryValidation --> [*]
    }
    
    StoryDiscovery --> DeepResearch: final_story_list ready
    
    state DeepResearch {
        [*] --> CreateStoryLoops
        CreateStoryLoops --> ParallelStoryResearch: LoopAgents created
        
        state ParallelStoryResearch {
            [*] --> Story1Loop
            [*] --> Story2Loop
            [*] --> StoryNLoop
            
            state Story1Loop {
                [*] --> DeepSearch1
                DeepSearch1 --> QualityCheck1
                QualityCheck1 --> QualitySufficient1: Assess quality
                QualitySufficient1 --> DeepSearch1: No - Continue loop
                QualitySufficient1 --> Story1Complete: Yes - Escalate
                Story1Complete --> [*]
            }
            
            state Story2Loop {
                [*] --> DeepSearch2
                DeepSearch2 --> QualityCheck2
                QualityCheck2 --> QualitySufficient2: Assess quality
                QualitySufficient2 --> DeepSearch2: No - Continue loop
                QualitySufficient2 --> Story2Complete: Yes - Escalate
                Story2Complete --> [*]
            }
            
            state StoryNLoop {
                [*] --> DeepSearchN
                DeepSearchN --> QualityCheckN
                QualityCheckN --> QualitySufficientN: Assess quality
                QualitySufficientN --> DeepSearchN: No - Continue loop
                QualitySufficientN --> StoryNComplete: Yes - Escalate
                StoryNComplete --> [*]
            }
            
            Story1Loop --> AllStoriesComplete
            Story2Loop --> AllStoriesComplete
            StoryNLoop --> AllStoriesComplete
        }
        
        ParallelStoryResearch --> [*]: story_research_outputs saved
    }
    
    DeepResearch --> NewsletterSynthesis: All story research complete
    
    state NewsletterSynthesis {
        [*] --> ReadStoryOutputs
        ReadStoryOutputs --> SynthesizeNewsletter
        SynthesizeNewsletter --> FormatOutput
        FormatOutput --> [*]: final_newsletter saved
    }
    
    NewsletterSynthesis --> [*]: Newsletter complete
    
    note right of StoryDiscovery
        Key Innovation: Stories discovered
        and validated BEFORE deep research
    end note
    
    note right of DeepResearch
        Quality assessment happens PER STORY
        during deep research, not globally
    end note
```

## Session State Evolution

The session state progresses through these key stages:

1. **user_requirements** → Initial natural language newsletter requirements
2. **research_plan** → ResearchPlanner's coordination strategy  
3. **topic_areas** → Identified topic areas for story discovery
4. **discovered_stories** → Initial story discoveries from WideSearchAgents
5. **final_story_list** → Validated and regrouped stories for deep research
6. **story_research_outputs** → Completed research for each story from LoopAgents
7. **final_newsletter** → FlexibleNewsletterSynthesizer's formatted output

## Key State Transition Principles

- **Linear Progression**: Each state builds on the previous one
- **Validation Gates**: Story validation can loop back for corrections
- **Per-Story Quality**: Each story has independent quality assessment loops
- **Clear Handoffs**: Each phase saves its output for the next phase to consume
