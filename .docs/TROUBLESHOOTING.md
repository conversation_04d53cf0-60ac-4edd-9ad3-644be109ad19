# Troubleshooting Guide

## What This Document Is For

Fix common problems with agent development, testing, debugging, and deployment of the newsletter generation system.

## What You'll Accomplish

- Diagnose and fix common agent issues
- Debug orchestration problems
- Resolve API and environment issues
- Test and validate your implementations
- Deploy and monitor the system

## Prerequisites

- Completed [SETUP.md](SETUP.md) and attempted [BUILDING_ORCHESTRATORS.md](BUILDING_ORCHESTRATORS.md)
- Basic understanding of error messages and logs

## Common Agent Issues

### Agent Not Responding or Hanging

**Symptoms**: Agent starts but never returns results, process hangs indefinitely

**Causes & Solutions**:

```python
# ❌ Missing output_key causes agent to not save results
agent = LlmAgent(
    name="MyA<PERSON>",
    instruction="Do research"
    # Missing output_key!
)

# ✅ Always include output_key for state persistence
agent = LlmAgent(
    name="MyA<PERSON>",
    instruction="Do research",
    output_key="research_results"  # Required!
)
```

**Check timeout settings**:

```bash
# Add to .env if agents are timing out
ADK_AGENT_TIMEOUT=600  # Increase timeout to 10 minutes
```

### Tool Escalation Not Working

**Symptoms**: LoopAgent runs maximum iterations instead of exiting when quality is sufficient

**Causes & Solutions**:

```python
# ❌ Wrong approach - using tools with hardcoded returns
def exit_research_loop(tool_context):
    return {"status": "done"}  # Violates agentic principles!

# ✅ Proper agentic approach - custom BaseAgent with Event escalation
class QualityAssessmentAgent(BaseAgent):
    async def _run_async_impl(self, ctx) -> AsyncGenerator[Event, None]:
        # LLM evaluates quality in natural language
        assessment = "SUFFICIENT - meets user requirements..."
        is_sufficient = assessment.startswith("SUFFICIENT")

        yield Event(
            author=self.name,
            actions=EventActions(escalate=is_sufficient)  # ADK-native escalation
        )
```

**Use agentic quality assessment in LoopAgent**:

```python
# ✅ Proper agentic approach with custom BaseAgent
quality_assessment_agent = QualityAssessmentAgent(
    name="AgenticQualityAssessor",
    description="Evaluates quality using LLM intelligence"
)

# Use in LoopAgent for iterative improvement
loop_agent = LoopAgent(
    name="QualityEnhancedResearch",
    sub_agents=[research_agent, quality_assessment_agent],
    max_iterations=3
)
```

### Session State Issues

**Symptoms**: Agents can't access data from previous agents, "KeyError" on session state

**Debugging session state**:

```python
# Add debugging to see what's in session state
async def debug_session_state(ctx):
    print("Session state keys:", list(ctx.session.state.keys()))
    print("Session state content:", ctx.session.state)

    # ADK best practice: Inspect state scopes
    print("Session-scoped keys:", [k for k in ctx.session.state.keys() if not k.startswith(('user:', 'app:', 'temp:'))])
    print("User-scoped keys:", [k for k in ctx.session.state.keys() if k.startswith('user:')])
    print("App-scoped keys:", [k for k in ctx.session.state.keys() if k.startswith('app:')])
    print("Temporary keys:", [k for k in ctx.session.state.keys() if k.startswith('temp:')])

# Check if required keys exist before accessing
user_requirements = ctx.session.state.get("user_requirements", "")
if not user_requirements:
    print("Warning: No user requirements found in session state")

# Verify output_key persistence after agent execution (ADK pattern)
async def verify_output_key_persistence(session_service, app_name, user_id, session_id, expected_key):
    """Verify that output_key was properly saved to session state"""
    updated_session = await session_service.get_session(
        app_name=app_name, user_id=user_id, session_id=session_id
    )
    if updated_session:
        value = updated_session.state.get(expected_key, "NOT_FOUND")
        print(f"Output key '{expected_key}': {value}")
        return value != "NOT_FOUND"
    else:
        print(f"❌ Could not retrieve session for output_key verification")
        return False
```

**🚨 MANDATORY: Proper Error Handling for Missing Inputs**:

```python
# ❌ WRONG - Accessing non-existent keys
requirements = ctx.session.state["user_requirements"]  # May not exist

# ❌ WRONG - Silent fallback that masks problems
requirements = ctx.session.state.get("user_requirements", "default")

# ✅ CORRECT - Explicit error handling (MANDATORY)
requirements = ctx.session.state.get("user_requirements", "")
if not requirements:
    raise ValueError("No user requirements found for research coordination")
```

### Input Validation Errors

**Symptoms**: Agents produce poor quality outputs, unclear error messages, silent failures

**🚨 MANDATORY Error Handling Patterns**:

```python
# ✅ CORRECT - Validate ALL critical inputs at start
async def _run_async_impl(self, ctx):
    # Get all required inputs
    user_requirements = ctx.session.state.get("user_requirements", "")
    research_plan = ctx.session.state.get("research_plan", "")

    # Validate each required input with specific error messages
    if not user_requirements:
        raise ValueError("No user requirements found for research coordination")
    if not research_plan:
        raise ValueError("No research plan found for research coordination")

    # Only proceed if all inputs are valid
    # ... rest of implementation

# ❌ WRONG - Proceeding with incomplete data
async def _run_async_impl(self, ctx):
    user_requirements = ctx.session.state.get("user_requirements", "default requirements")
    # This masks the real problem and produces poor quality outputs!
```

**Error Message Quality Standards**:

```python
# ✅ CORRECT - Specific, actionable error messages
raise ValueError("No user requirements found for research coordination")
raise ValueError("Research plan is empty - cannot coordinate research")
raise TypeError("Expected string for user_requirements, got {type(value)}")

# ❌ WRONG - Generic, unhelpful messages
raise ValueError("Invalid input")
raise Exception("Something went wrong")
raise RuntimeError("Error occurred")
```

## API and Environment Issues

### Exa API Errors

**Symptoms**: "API key invalid", "Rate limit exceeded", "No results found"

**Check API key configuration**:

```bash
# Verify .env file has correct key
cat .env | grep EXA_API_KEY

# Test API key directly
poetry run python -c "
import os
from underlines_adk.tools.exa_tools import exa_wide_search
print('Testing Exa API...')
result = exa_wide_search('AI news', 7)
print('✅ Exa API working')
"
```

**Rate limiting solutions**:

```bash
# Add rate limiting configuration to .env
EXA_RATE_LIMIT_DELAY=1  # Seconds between requests
EXA_MAX_RETRIES=3       # Retry attempts
```

### Google API / Gemini Issues

**Symptoms**: "Invalid API key", "Model not found", "Quota exceeded"

**Verify Google API setup**:

```bash
# Test Google API key
poetry run python -c "
import google.generativeai as genai
import os
genai.configure(api_key=os.getenv('GOOGLE_API_KEY'))
model = genai.GenerativeModel('gemini-pro')
response = model.generate_content('Hello')
print('✅ Google API working:', response.text[:50])
"
```

**Common Google API issues**:

- **Wrong model name**: Use `gemini-pro` or `gemini-pro-vision`
- **Quota exceeded**: Check your Google AI Studio quota
- **Region restrictions**: Some regions may have limited access

### Environment Variable Issues

**Symptoms**: "Environment variable not found", agents using default values

**Debug environment loading**:

```python
import os
print("Current working directory:", os.getcwd())
print("EXA_API_KEY set:", bool(os.getenv("EXA_API_KEY")))
print("GOOGLE_API_KEY set:", bool(os.getenv("GOOGLE_API_KEY")))

# Check if .env file exists and is readable
import pathlib
env_file = pathlib.Path(".env")
print(".env file exists:", env_file.exists())
if env_file.exists():
    print(".env file readable:", env_file.is_file())
```

## Testing and Validation

### Unit Testing Agents

```python
import pytest
import asyncio
from underlines_adk.sessions import LoggedSessionService
from google.adk.runners import Runner

@pytest.mark.asyncio
async def test_wide_search_agent():
    """Test WideSearchAgent with known query"""
    from underlines_adk.agents.wide_search_agent import exa_agent as wide_search_agent

    session_service = LoggedSessionService()
    runner = Runner(
        agent=wide_search_agent,
        app_name="test_app",
        session_service=session_service
    )
    
    # Test with simple query
    test_query = '{"query": "Python programming", "lookback_days": 7}'
    
    session = await session_service.create_session(
        app_name="test_app",
        user_id="test_user"
    )
    
    results = []
    async for event in runner.run_async(
        user_id="test_user",
        session_id=session.id,
        new_message=types.Content(parts=[types.Part(text=test_query)])
    ):
        if event.is_final_response():
            results.append(event.content.parts[0].text)
            break
    
    assert len(results) > 0
    assert "title" in results[0]  # Should contain structured results
```

### Integration Testing

```python
@pytest.mark.asyncio
async def test_newsletter_pipeline():
    """Test complete newsletter generation pipeline"""
    pipeline = create_flexible_newsletter_pipeline()
    
    # Test with realistic requirements
    user_requirements = """
    Weekly AI newsletter with 3 major developments, 
    focus on business applications and market impact.
    """
    
    # Run pipeline and verify output
    # ... (similar to unit test pattern)
```

### Debugging with Logs

```bash
# Enable debug logging
export ADK_SESSION_LOG_LEVEL=DEBUG

# Run your agent and check logs
poetry run python your_agent_script.py

# Check session logs
ls -la session_logs/
tail -f session_logs/latest.log
```

## Performance and Monitoring

### Monitoring Agent Performance

```python
import time
from functools import wraps

def monitor_agent_performance(func):
    """Decorator to monitor agent execution time"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            execution_time = time.time() - start_time
            print(f"Agent {func.__name__} completed in {execution_time:.2f}s")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            print(f"Agent {func.__name__} failed after {execution_time:.2f}s: {e}")
            raise
    return wrapper
```

### ADK-Specific Debugging Patterns

**Event Processing Debugging**:

```python
# Debug event flow in agent execution
async def debug_agent_events(ctx):
    """Debug event processing and state updates"""
    print(f"Session events count: {len(ctx.session.events)}")

    # Check recent events
    if ctx.session.events:
        latest_event = ctx.session.events[-1]
        print(f"Latest event author: {latest_event.author}")
        print(f"Latest event actions: {latest_event.actions}")
        if latest_event.actions and latest_event.actions.state_delta:
            print(f"State delta: {latest_event.actions.state_delta}")

# Verify state persistence after yield
async def debug_state_after_yield(ctx, expected_key):
    """Verify state changes are committed after yielding events"""
    # This should be called after yielding an event with state_delta
    current_value = ctx.session.state.get(expected_key, "NOT_FOUND")
    print(f"State after yield - {expected_key}: {current_value}")
    return current_value != "NOT_FOUND"
```

**Session Service Debugging**:

```python
# Debug session retrieval and updates
async def debug_session_service(session_service, app_name, user_id, session_id):
    """Debug session service operations"""
    try:
        session = await session_service.get_session(app_name, user_id, session_id)
        if session:
            print(f"✅ Session retrieved: {session.id}")
            print(f"Last update: {session.last_update_time}")
            print(f"Events count: {len(session.events)}")
            print(f"State keys: {list(session.state.keys())}")
        else:
            print(f"❌ Session not found: {app_name}/{user_id}/{session_id}")
    except Exception as e:
        print(f"❌ Session service error: {e}")
```

### Cost Monitoring

```bash
# Monitor API usage and costs
# Add to .env for cost tracking
TRACK_API_COSTS=true
LOG_TOKEN_USAGE=true

# Check logs for cost information
grep "tokens" session_logs/*.log
grep "cost" session_logs/*.log
```

## Deployment Issues

### Production Environment Setup

```bash
# Production environment variables
export ENVIRONMENT=production
export ADK_SESSION_LOG_LEVEL=INFO  # Less verbose in production
export MAX_RESEARCH_ITERATIONS=2   # Lower limits for cost control
export ADK_AGENT_TIMEOUT=180       # Shorter timeouts

# Verify production setup
poetry run python -c "
import os
print('Environment:', os.getenv('ENVIRONMENT'))
print('Log level:', os.getenv('ADK_SESSION_LOG_LEVEL'))
print('Max iterations:', os.getenv('MAX_RESEARCH_ITERATIONS'))
"
```

### Docker Deployment

```dockerfile
# Example Dockerfile for deployment
FROM python:3.13-slim

WORKDIR /app
COPY pyproject.toml poetry.lock ./
RUN pip install poetry && poetry install --no-dev

COPY . .
ENV ENVIRONMENT=production
ENV ADK_SESSION_LOG_LEVEL=INFO

CMD ["poetry", "run", "python", "main.py"]
```

### Health Checks

```python
async def health_check():
    """Basic health check for deployment"""
    try:
        # Test basic agent functionality
        from underlines_adk.agents.wide_search_agent import exa_agent as wide_search_agent
        # Run simple test query
        # Return success/failure status
        return {"status": "healthy", "timestamp": time.time()}
    except Exception as e:
        return {"status": "unhealthy", "error": str(e), "timestamp": time.time()}
```

## Getting Help

### Log Analysis

```bash
# Find error patterns in logs
grep -i "error" session_logs/*.log
grep -i "exception" session_logs/*.log
grep -i "timeout" session_logs/*.log

# Check for specific agent issues
grep "WideSearchAgent" session_logs/*.log
grep "DeepSearchAgent" session_logs/*.log
grep "ResearchPlanner" session_logs/*.log
grep "DynamicResearchCoordinator" session_logs/*.log
```

### Debug Information to Collect

When reporting issues, include:

1. **Error message**: Full stack trace
2. **Environment**: Python version, Poetry version, OS
3. **Configuration**: Relevant .env variables (without API keys)
4. **Logs**: Recent session logs showing the issue
5. **Code**: Minimal example that reproduces the problem

### Common Solutions Summary

- **Agent hanging**: Check output_key and timeouts
- **Tool escalation failing**: Use `Event(actions=EventActions(escalate=True))` pattern (see QualityAssessmentAgent)
- **🚨 Session state errors**: Use `.get()` then validate with explicit error handling (MANDATORY)
- **🚨 Input validation**: Always validate critical inputs and raise clear errors (MANDATORY)
- **API errors**: Verify keys and check quotas
- **Performance issues**: Monitor execution time and token usage
- **🚨 Poor quality outputs**: Often caused by missing input validation - check error handling
- **🚨 Anti-pattern violations**: Consult [ANTI_PATTERNS_AND_VALIDATION.md](ANTI_PATTERNS_AND_VALIDATION.md) for comprehensive guidance

## Next Steps

- **Return to development**: Go back to [BUILDING_ORCHESTRATORS.md](BUILDING_ORCHESTRATORS.md)
- **Check existing capabilities**: Review [EXISTING_AGENTS.md](EXISTING_AGENTS.md)
- **Environment issues**: Revisit [SETUP.md](SETUP.md)
