# Fractal README Specification (v 1.0)

> **One lightweight pattern for humans *and* AIs.**  
> Pure GitHub-Flavored Markdown + (optional) HTML comment anchors.

---

## 1 · Core Philosophy

* Keep knowledge **where the code lives**.  
* Make docs ***pleasant*** for humans to read on GitHub.  
* Give parsers / LLMs a stable layout without forcing YAML.

## 2 · Required & Recommended Headings

Use **level-3 headings** (`###`) with the exact names below.

| Heading              | Purpose & Guidance                                           | Required for |
|----------------------|--------------------------------------------------------------|--------------|
| **Purpose**          | 1–3 sentences: goal (+ optional scope line).                 | all levels |
| **Architecture**     | High-level design & flow; Recommended: include a mermaid diagram.            | project · directory · file |
| **Contents**         | `tree` file structure.       | project · directory |
| **Test Refs**        | Links to unit / integration / E2E tests.                     | all levels |
| **Doc Refs**         | Links to specs / PRDs / ADRs / wiki pages.                   | all levels |
| **Dependent Refs**   | Who imports or calls this code.                              | recommended |
| **Examples**  | Sections support any reStructuredText formatting, including literal blocks. | recommended |
| **Todos**  | Any work remaining. | optional |
| **Custom …**         | Any extra sections. Use clear slugs.                         | optional, unlimited |

*Order is **flexible**.*  
If a heading exists but is empty → add a TODO.

## 3 · Optional HTML Anchors

Anchors make parsing rock-solid without disturbing Markdown render.

```markdown
### Purpose
<!-- readme:purpose-start -->
Why this unit exists … (human prose)
<!-- readme:purpose-end -->
```

Supported slugs:

purpose
architecture
doc-refs
test-refs
dependent-refs
examples
todos
custom-<slug>

Anchors are optional, but recommended for any section you expect tools to extract.

⸻

## 4 · Diagrams (Mermaid)

* One primary diagram per Diagram section or inline inside Architecture.
* Use fenced mermaid blocks.
* ~60 lines → consider linking out to a separate file referenced in Doc Refs.

⸻

## 5 · Directory-Level README Example

### Purpose
<!-- readme:purpose-start -->
Image-resizer micro-service — generates thumbnails for user uploads.
Handles JPEG/PNG only; writes to S3; horizontally scalable via Celery.
<!-- readme:purpose-end -->

### Architecture
<!-- readme:architecture-start -->
FastAPI endpoint publishes `image.resize` jobs → Celery workers →
Pillow → S3.

```mermaid
sequenceDiagram
  Client ->> API: POST /upload
  API ->> MQ: publish job
  Worker ->> MQ: consume
  Worker ->> S3: PUT thumbnail
```
<!-- readme:architecture-end -->

### Contents
<!-- readme:contents-start -->
```text
project-root/
├── api_handler.py        # FastAPI upload route
├── worker.py             # Celery consumer, resize & upload
├── config.py             # env vars, queue names, bucket names
└── tests/                # unit + integration tests
```
<!-- readme:contents-end -->

### Doc Refs
<!-- readme:doc-refs-start -->
* docs/image_processing.md – original requirements
* docs/s3_upload_pattern.md – internal guidelines
<!-- readme:doc-refs-end -->

### Test Refs
<!-- readme:test-refs-start -->
* tests/unit/test_worker.py
* tests/integration/test_image_pipeline.py
<!-- readme:test-refs-end -->

### Dependent Refs
<!-- readme:dependent-refs-start -->
* ../frontend_api/routes.py#L47 – calls resize_image
* ../cron/cleanup.py#L12 – imports S3_CLIENT
<!-- readme:dependent-refs-end -->

### Examples
<!-- readme:examples-start -->
```python
# Example usage of resize_image function
from image_resizer import resize_image

# Resize an image to multiple sizes
sizes = [(100, 100), (200, 200), (300, 300)]
thumbnails = resize_image('path/to/image.jpg', sizes)
```
<!-- readme:examples-end -->

### Todos
<!-- readme:todos-start -->
* Add support for TIFF uploads
* Implement S3 presigned URL generation
<!-- readme:todos-end -->

## 6 · Function-Level Example (Python docstring)

Respect language conventions for class- and function-level docstrings.

```python
def resize_image(path: str, sizes: list[tuple[int,int]]) -> list[str]:
    """Generates multiple thumbnails preserving aspect ratio.

    Args:
        path (str): Source image path.
        sizes (list[tuple[int, int]]): Requested dimensions as (width, height) tuples.

    Returns:
        list[str]: S3 URLs of generated thumbnails.

    Raises:
        ImageCorruptError: If Pillow cannot decode input.
 
    Note:
        Does not support animated GIFs.
    """
```

## 7 · Directory Size Guidance

* ≤ 5 children → fine
* 5-8 → consider sub-folders
* ≥ 9 → add a ### Custom Directory Size Override with rationale.

## 8 · Future Work (non-blocking)

* Lint tool (readme-lint) to enforce mandatory headings & anchor pairing.
* Migration helper to wrap legacy docs with anchors and stub missing sections.
* Auto-gen Dependent Refs via static-analysis.
* IDE snippets for quick section insertion.

⸻

Adopt gradually: start by adding Purpose · Architecture · Contents to any README that lacks them.
Everything else is additive. Your codebase becomes a navigable knowledge graph for teammates ✨ and for LLM copilots 🤖.
