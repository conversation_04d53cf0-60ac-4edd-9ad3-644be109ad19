# Underlines ADK Development Roadmap

## Task Tracking and Progress Management

<!-- purpose-start -->
Comprehensive task tracking for Underlines ADK development, organized by phases and priorities. Each task represents approximately 1 day of work with clear acceptance criteria and dependencies.
<!-- purpose-end -->

---

## Current Status (2025-01-09)

### ✅ Completed Foundation Work

- [x] **DeepSearchAgent Implementation** - Comprehensive search with citations
- [x] **WideSearchAgent Implementation** - Broad topic coverage with universal flexibility
- [x] **Context Window Optimization** - Fixed with Gemini Flash and optimized result counts
- [x] **Comprehensive Testing Suite** - Unit and integration tests passing
- [x] **Project Documentation** - README, architecture, and agent patterns documented
- [x] **PRD and Tech Spec Creation** - Product requirements and technical specifications defined
- [x] **Critical Architecture Documentation Updates** - Added pipeline flexibility requirements
- [x] **Flexible Newsletter Pipeline Design** - Comprehensive design for universal newsletter generation
- [x] **Pipeline Flexibility Requirements Documentation** - Critical guidelines to prevent hardcoding
- [x] **Documentation Streamlining (2025-01-09)** - Eliminated duplication, improved navigation, consolidated anti-patterns
- [x] **ResearchPlanner Implementation** - Natural language research planning orchestrator
- [x] **QualityAssessmentAgent Implementation** - Agentic quality evaluation with LLM intelligence
- [x] **Basic DynamicResearchCoordinator** - Simple ParallelAgent coordination (INCOMPLETE - needs story-centric workflow)

### 🚨 CRITICAL GAPS IDENTIFIED: Documentation vs Implementation Misalignment

**URGENT**: Major discrepancies found between pristine documentation and current codebase implementation. The current implementation is missing the core story-centric workflow architecture that defines our entire system.

**Critical Missing Components**:

- ❌ **Story-Centric Workflow**: Current DynamicResearchCoordinator only does simple parallel coordination, missing 3 of 4 required phases
- ❌ **FlexibleNewsletterSynthesizer**: Completely missing - no implementation exists
- ❌ **Session State Architecture**: Missing 5 of 7 required session state keys and progression logic
- ❌ **Complete Pipeline Integration**: No end-to-end SequentialAgent pipeline implementation
- ❌ **Multi-Stage Coordination**: No topic analysis, story discovery, validation, or per-story research phases

**Architecture Compliance Status**:

- ✅ Universal natural language interface (ResearchPlanner implemented)
- ❌ Story-centric workflow with four distinct phases (NOT IMPLEMENTED)
- ❌ Session state progression through 7 defined keys (NOT IMPLEMENTED)
- ❌ Per-story quality assessment (NOT IMPLEMENTED)
- ❌ Complete pipeline orchestration (NOT IMPLEMENTED)

**Reference Documents**:

- [PRD.md](../.docs/PRD.md) - Defines story-centric workflow architecture
- [EXISTING_AGENTS.md](../.docs/EXISTING_AGENTS.md) - Documents required agent capabilities
- [BUILDING_ORCHESTRATORS.md](../.docs/BUILDING_ORCHESTRATORS.md) - Implementation patterns and code examples
- [ANTI_PATTERNS_AND_VALIDATION.md](../.docs/ANTI_PATTERNS_AND_VALIDATION.md) - Validation requirements

---

## IMMEDIATE PRIORITY: Story-Centric Architecture Implementation

### 🚨 CRITICAL ISSUE: Implementation Does Not Match Documentation

**Problem**: Current codebase implements simple agent coordination but is missing the core story-centric workflow architecture that defines our entire system according to the documentation.

**Impact**: Without story-centric workflow, we cannot deliver on the product vision of universal newsletter generation with proper story discovery, validation, and per-story quality assessment.

**Required Action**: Complete reimplementation of DynamicResearchCoordinator and implementation of missing components to match documented architecture.

---

## Phase 1: Story-Centric Architecture Alignment (URGENT - Q1 2025)

### Step 1: Story-Centric DynamicResearchCoordinator Implementation (3 days) 🚨 CRITICAL

**URGENT**: Current DynamicResearchCoordinator is a simple ParallelAgent wrapper. Must be completely reimplemented to match the documented multi-stage orchestrator architecture.

#### Day 1: Multi-Stage Workflow Foundation ✅ COMPLETE

- [x] **Reimplement DynamicResearchCoordinator with Story-Centric Architecture**
  - [x] Replace current simple ParallelAgent coordination with multi-stage workflow
  - [x] Implement Phase 1: Story Discovery (topic analysis → parallel WideSearchAgents → story aggregation)
  - [x] Implement Phase 2: Story Validation & Regrouping (validation with iteration capability)
  - [x] Add session state management for `topic_areas`, `discovered_stories`, `final_story_list`
  - [x] Create topic area identification logic using LLM intelligence
  - [x] Implement story aggregation and validation against user requirements
  - [x] Add iteration capability for corrective instructions when stories are insufficient
  - [x] Ensure all session state keys match PRD specification exactly
  - [x] Update unit tests to validate multi-stage workflow
  - **Acceptance Criteria**: ✅ DynamicResearchCoordinator implements first 2 phases of story-centric workflow
  - **Dependencies**: None (blocking all other work)
  - **Key Pattern**: Multi-stage BaseAgent with proper session state progression
  - **Validation**: ✅ Passes story-centric workflow validation checklist from ANTI_PATTERNS_AND_VALIDATION.md
  - **Commit**: `feat: implement story-centric workflow phases 1-2 in DynamicResearchCoordinator`

#### Day 2: Per-Story Deep Research Implementation ⚠️ BLOCKING

- [ ] **Implement Phase 3: Deep Research Per Story**
  - [ ] Create parallel LoopAgent instances for each story in `final_story_list`
  - [ ] Implement per-story quality assessment with DeepSearchAgent + QualityAssessmentAgent
  - [ ] Add `story_research_outputs` session state management
  - [ ] Ensure each story gets independent quality assessment loop
  - [ ] Validate quality assessment timing (per story during research, not after completion)
  - [ ] Test with multiple stories to ensure parallel processing works correctly
  - [ ] Update integration tests to validate per-story research workflow
  - **Acceptance Criteria**: Each story gets its own LoopAgent with quality assessment
  - **Dependencies**: Day 1 completion
  - **Key Pattern**: ParallelAgent containing multiple LoopAgents with per-story quality assessment
  - **Validation**: Quality assessment happens per story during deep research phase
  - **Commit**: `feat: implement per-story deep research with quality assessment`

#### Day 3: Complete Multi-Stage Integration ⚠️ BLOCKING

- [ ] **Complete DynamicResearchCoordinator Multi-Stage Implementation**
  - [ ] Integrate all three phases into cohesive workflow
  - [ ] Add proper handoff to FlexibleNewsletterSynthesizer (Phase 4)
  - [ ] Validate complete session state progression through all 7 keys
  - [ ] Test end-to-end workflow with diverse newsletter types
  - [ ] Ensure backward compatibility with existing WideSearchAgent and DeepSearchAgent
  - [ ] Update documentation to reflect actual implementation
  - [ ] Add comprehensive integration tests for complete workflow
  - **Acceptance Criteria**: DynamicResearchCoordinator implements complete story-centric workflow
  - **Dependencies**: Day 2 completion
  - **Key Pattern**: Complete multi-stage orchestrator matching PRD architecture
  - **Validation**: All session state keys used in correct sequence
  - **Commit**: `feat: complete story-centric workflow implementation`

### Step 2: FlexibleNewsletterSynthesizer Implementation (2 days) 🚨 CRITICAL

**URGENT**: This component is completely missing but is required for end-to-end pipeline functionality.

#### Day 1: FlexibleNewsletterSynthesizer Core Implementation ⚠️ BLOCKING

- [ ] **Implement FlexibleNewsletterSynthesizer LlmAgent**
  - [ ] Create `underlines_adk/agents/flexible_newsletter_synthesizer.py`
  - [ ] Implement LlmAgent that reads `story_research_outputs` and `user_requirements` from session state
  - [ ] Add universal format flexibility - adapts to any newsletter type based on user requirements
  - [ ] Implement context-aware synthesis using user requirements and completed story research
  - [ ] Add proper error handling for missing inputs (story_research_outputs, user_requirements)
  - [ ] Use `final_newsletter` as output_key for session state
  - [ ] Ensure no hardcoded newsletter formats or structures
  - [ ] Test with diverse newsletter types to validate universal flexibility
  - **Acceptance Criteria**: FlexibleNewsletterSynthesizer creates formatted newsletters from story research
  - **Dependencies**: None (can be implemented independently)
  - **Key Pattern**: LlmAgent with session state reading and universal format adaptation
  - **Validation**: Must work with any newsletter type without modification
  - **Commit**: `feat: implement FlexibleNewsletterSynthesizer for universal newsletter generation`

#### Day 2: Newsletter Synthesis Integration & Testing ⚠️ BLOCKING

- [ ] **Complete Newsletter Synthesis Integration**
  - [ ] Integrate FlexibleNewsletterSynthesizer with existing session state architecture
  - [ ] Test synthesis with outputs from current DynamicResearchCoordinator
  - [ ] Validate newsletter format adaptation based on user requirements
  - [ ] Add comprehensive unit tests for synthesis functionality
  - [ ] Test with multiple newsletter types (daily news, biotech VC, fintech)
  - [ ] Ensure quality preservation while adapting format to user specifications
  - [ ] Update agents module to include flexible_newsletter_synthesizer
  - [ ] Add integration tests for complete research → synthesis workflow
  - **Acceptance Criteria**: FlexibleNewsletterSynthesizer integrates with existing workflow
  - **Dependencies**: Day 1 completion
  - **Key Pattern**: Session state integration with universal format flexibility
  - **Validation**: Maintains research quality while adapting to user specifications
  - **Commit**: `feat: integrate newsletter synthesis with research workflow`

### Step 3: Complete Pipeline Integration (2 days) 🚨 CRITICAL

**URGENT**: No end-to-end pipeline implementation exists despite being documented.

#### Day 1: SequentialAgent Pipeline Implementation ⚠️ BLOCKING

- [ ] **Implement Complete Story-Centric Newsletter Pipeline**
  - [ ] Create `create_story_centric_newsletter_pipeline()` function as documented
  - [ ] Implement SequentialAgent combining ResearchPlanner → DynamicResearchCoordinator → FlexibleNewsletterSynthesizer
  - [ ] Add proper session state initialization with `user_requirements`
  - [ ] Validate complete session state progression through all 7 keys
  - [ ] Test end-to-end pipeline with natural language newsletter requirements
  - [ ] Ensure pipeline works with any newsletter type without modification
  - [ ] Add comprehensive integration tests for complete pipeline
  - [ ] Create example script demonstrating complete pipeline usage
  - **Acceptance Criteria**: Complete pipeline generates newsletters from natural language requirements
  - **Dependencies**: Steps 1-2 completion
  - **Key Pattern**: SequentialAgent orchestrating complete story-centric workflow
  - **Validation**: End-to-end functionality matching documented architecture
  - **Commit**: `feat: implement complete story-centric newsletter pipeline`

#### Day 2: Pipeline Validation & Documentation Alignment ⚠️ BLOCKING

- [ ] **Validate Complete Architecture Alignment**
  - [ ] Test pipeline against all validation checklists in ANTI_PATTERNS_AND_VALIDATION.md
  - [ ] Validate story-centric workflow compliance with PRD architecture
  - [ ] Test with diverse newsletter types to ensure universal flexibility
  - [ ] Update all documentation to reflect actual implementation
  - [ ] Add comprehensive usage examples and integration tests
  - [ ] Validate session state management matches PRD specification
  - [ ] Ensure all anti-patterns are avoided in implementation
  - [ ] Create deployment-ready pipeline configuration
  - **Acceptance Criteria**: Implementation fully matches documented architecture
  - **Dependencies**: Day 1 completion
  - **Key Pattern**: Complete architecture validation and documentation alignment
  - **Validation**: All documentation validation checklists pass
  - **Commit**: `docs: align implementation with story-centric architecture documentation`

---

## Phase 2: Foundation Validation & Stabilization (Q1 2025 - After Core Implementation)

### Step 4: Architecture Validation & Testing (2 days)

#### Day 1: Comprehensive Architecture Testing

- [ ] **Validate Complete Story-Centric Architecture**
  - [ ] Run comprehensive test suite against all implemented components
  - [ ] Validate session state progression through all 7 keys
  - [ ] Test story-centric workflow with multiple newsletter types
  - [ ] Verify quality assessment timing (per story during research)
  - [ ] Validate universal flexibility across diverse use cases
  - [ ] Test error handling and edge cases
  - [ ] Performance testing with realistic workloads
  - **Acceptance Criteria**: All architecture validation tests pass
  - **Dependencies**: Phase 1 completion
  - **Key Pattern**: Comprehensive validation of story-centric workflow
  - **Commit**: `test: comprehensive architecture validation suite`

#### Day 2: Documentation & Implementation Alignment

- [ ] **Final Documentation Alignment**
  - [ ] Update all documentation to reflect actual implementation
  - [ ] Validate all code examples in documentation work correctly
  - [ ] Update README with accurate usage instructions
  - [ ] Create comprehensive deployment guide
  - [ ] Add troubleshooting guide for common issues
  - [ ] Validate all anti-pattern prevention guidelines
  - [ ] Create final architecture validation report
  - **Acceptance Criteria**: Documentation perfectly matches implementation
  - **Dependencies**: Day 1 completion
  - **Key Pattern**: Complete documentation-implementation alignment
  - **Commit**: `docs: final alignment with story-centric implementation`

### Step 5: Production Readiness (1 day)

- [ ] **Production Deployment Preparation**
  - [ ] Add comprehensive error handling and logging
  - [ ] Implement monitoring and health checks
  - [ ] Add configuration management for production
  - [ ] Create deployment scripts and documentation
  - [ ] Add security considerations and API key management
  - [ ] Performance optimization and resource management
  - [ ] Create backup and recovery procedures
  - **Acceptance Criteria**: System ready for production deployment
  - **Dependencies**: Step 4 completion
  - **Key Pattern**: Production-ready story-centric newsletter system
  - **Commit**: `feat: production readiness for story-centric pipeline`

---

## Future Phases (Q2+ 2025) - After Core Architecture Complete

### Phase 3: Advanced Features & Enhancement

- [ ] **Enhanced Natural Language Processing**
  - [ ] Advanced requirement parsing for complex specifications
  - [ ] Multi-language newsletter requirements support
  - [ ] Intelligent requirement clarification

- [ ] **Output Format Flexibility**
  - [ ] Multiple output formats (Markdown, HTML, PDF)
  - [ ] Email delivery integration
  - [ ] Newsletter archival system
  - [ ] Version control for newsletters

- [ ] **Advanced Quality Assessment**
  - [ ] Sophisticated citation validation
  - [ ] Bias detection across newsletter types
  - [ ] Fact-checking tools for different domains

### Phase 4: User Interface & Experience

- [ ] **Web Interface Development**
  - [ ] Web interface for newsletter creation
  - [ ] Newsletter preview and editing
  - [ ] User feedback and rating system
  - [ ] Dashboard for newsletter management

- [ ] **Personalization Engine**
  - [ ] User preference learning
  - [ ] Content recommendation algorithms
  - [ ] Adaptive quality thresholds
  - [ ] Usage analytics

### Phase 5: Enterprise & Scale Features

- [ ] **Enterprise Features**
  - [ ] Multi-user organization support
  - [ ] API access for enterprise customers
  - [ ] Advanced analytics and reporting
  - [ ] Custom branding and white-labeling

- [ ] **Advanced Intelligence**
  - [ ] Multi-language support
  - [ ] Advanced fact-checking algorithms
  - [ ] Predictive content recommendations
  - [ ] Automated quality improvement

---

## Development Guidelines

### 🚨 CRITICAL: Story-Centric Architecture Compliance

**BEFORE IMPLEMENTING ANY CODE**, validate against these requirements:

1. **Story-Centric Workflow Validation**: Must implement all 4 phases (Story Discovery → Story Validation & Regrouping → Deep Research Per Story → Newsletter Synthesis)
2. **Session State Compliance**: Must use all 7 PRD-defined session state keys in correct sequence
3. **Anti-Pattern Prevention**: Review [ANTI_PATTERNS_AND_VALIDATION.md](../.docs/ANTI_PATTERNS_AND_VALIDATION.md) validation checklists
4. **Universal Flexibility**: Must work for ANY newsletter type without modification
5. **ADK-Native Patterns**: Use SequentialAgent, ParallelAgent, LoopAgent appropriately

### Daily Workflow

1. **Start of Day**: Review current task and architecture requirements
2. **Implementation**: Focus on single task with story-centric compliance
3. **Architecture Validation**: Test against story-centric workflow requirements
4. **Testing**: Ensure all tests pass and architecture compliance is maintained
5. **Documentation**: Update relevant documentation to match implementation
6. **End of Day**: Update TODOS.md with progress and commit changes

### Quality Standards

- **MANDATORY**: All implementations must follow story-centric architecture
- **MANDATORY**: All session state management must use PRD-defined keys
- **MANDATORY**: All agents must implement explicit error handling
- All new code must have comprehensive tests across multiple newsletter types
- Documentation must be updated to reflect actual implementation
- Performance impact must be assessed and optimized
- Security considerations must be addressed universally

### Commit Strategy

- One commit per completed task using conventional commits format
- Clear commit messages referencing task completion and architecture compliance
- All tests must pass before committing
- Update TODOS.md in each commit
- **Format**: `feat: implement [feature]` or `docs: add [documentation]`

### Architecture Validation Checklist

Before any commit, verify:

- [ ] Story-centric workflow phases implemented correctly
- [ ] Session state keys match PRD specification
- [ ] Quality assessment happens per story during research
- [ ] Universal flexibility maintained
- [ ] Anti-patterns avoided
- [ ] ADK-native patterns used correctly
- [ ] Error handling implemented with clear ValueError messages

---

## Risk Mitigation

### Critical Risks Identified

1. **Architecture Misalignment**: Current implementation does not match documentation
   - **Mitigation**: Complete reimplementation following story-centric architecture
   - **Timeline**: Must be completed before any other work

2. **Session State Inconsistency**: Missing 5 of 7 required session state keys
   - **Mitigation**: Implement complete session state progression
   - **Validation**: Test all session state transitions

3. **Missing Core Components**: FlexibleNewsletterSynthesizer not implemented
   - **Mitigation**: Implement missing components as priority
   - **Dependencies**: Blocks end-to-end functionality

See [PRD.md](../.docs/PRD.md) for product constraints and [TROUBLESHOOTING.md](../.docs/TROUBLESHOOTING.md) for technical risk mitigation.

---

## Next Session Kickoff Plan

### Immediate Actions Required

1. **Start with Step 1, Day 1**: Reimplement DynamicResearchCoordinator with story-centric architecture
2. **Focus**: Multi-stage workflow foundation (Story Discovery + Story Validation phases)
3. **Validation**: Must pass story-centric workflow validation checklist
4. **Outcome**: DynamicResearchCoordinator that matches documented architecture

### Success Criteria for Next Session

- DynamicResearchCoordinator implements multi-stage workflow with proper session state management
- All session state keys from PRD are used correctly
- Story discovery and validation phases work with any newsletter type
- Implementation matches documentation exactly

---

*This roadmap reflects the ACTUAL status of development and required direction to align implementation with our pristine documentation. All work must focus on implementing the story-centric architecture as documented.*
