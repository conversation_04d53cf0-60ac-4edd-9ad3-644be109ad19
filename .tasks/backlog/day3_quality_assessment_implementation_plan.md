# Day 3: Quality Assessment Wrapper Implementation Plan

## Overview

**Task**: Implement Quality Assessment Layer Over Existing Agent Outputs  
**Date**: 2025-06-09  
**Phase**: Phase 1, Step 1, Day 3 of Flexible Newsletter Generation Pipeline

## Architectural Analysis

### Current Understanding from Documentation Review

**Key Requirements**:

1. **Tool Organization**: Follow patterns in `underlines_adk/tools/README.md` with pure functions, error handling, comprehensive documentation
2. **Google ADK LoopAgent Pattern**: Use `EventActions(escalate=True)` for termination, not ToolContext
3. **Existing Agent Integration**: Use WideSearchAgent and DeepSearchAgent through orchestration (no modification)
4. **Universal Flexibility**: Must work for ANY newsletter type, not hardcoded criteria
5. **Mandatory Error Handling**: Validate inputs and raise clear errors for missing data
6. **Agentic Principles**: Trust LLM judgment over programmatic validation

### Key Architectural Insights from Google ADK Documentation

1. **Tool Escalation Pattern**: Custom `BaseAgent` that yields `Event(actions=EventActions(escalate=True))`, NOT tool function with ToolContext
2. **LoopAgent Structure**: `LoopAgent(sub_agents=[existing_agents, quality_assessor], max_iterations=N)`
3. **Quality Assessment**: LlmAgent that evaluates outputs and calls custom BaseAgent for escalation
4. **Tool Organization**: Each tool should be standalone function following existing patterns in `exa_tools.py`

## Implementation Plan

### Step 1: Create Agentic Quality Assessment Agent (Custom BaseAgent)

**File**: `underlines_adk/agents/quality_assessment_agent.py` (NEW FILE)

**Class**: `QualityAssessmentAgent(BaseAgent)`

**Pattern**:

- Custom BaseAgent following ADK-native patterns
- Uses `Event(actions=EventActions(escalate=True))` for termination
- No hardcoded dictionary return structures
- Trusts LLM intelligence completely

**Purpose**: Evaluate research quality using LLM judgment and natural language assessment

**Key Requirements**:

- Must adapt to ANY newsletter type (biotech VC, fintech, academic, daily news)
- No hardcoded quality criteria or return structures
- Trust LLM intelligence for evaluation
- Return LLM's raw assessment without forcing predetermined formats
- Raise clear ValueError exceptions for missing inputs

### Step 2: Integrate with LoopAgent (ADK-Native Pattern)

**Approach**: Use QualityAssessmentAgent directly in LoopAgent - no separate tool files needed

**Integration Pattern**:

- Reads research outputs from session state
- Evaluates against user requirements using LLM intelligence
- Escalates when quality meets user expectations
- Follows ADK-native BaseAgent pattern (not tool-based approach)

### Step 3: Update Existing Orchestration Patterns

**Approach**: Update existing orchestration to use agentic quality assessment

**Pattern**:

```python
# Create agentic quality assessment agent
quality_agent = QualityAssessmentAgent(
    name="AgenticQualityAssessor",
    description="Evaluates research quality using LLM intelligence"
)

# Use in LoopAgent with existing agents
LoopAgent(
    sub_agents=[
        wide_search_agent,    # Existing WideSearchAgent instance
        deep_search_agent,    # Existing DeepSearchAgent instance
        quality_agent         # Agentic quality assessment with escalation
    ],
    max_iterations=int(os.getenv("MAX_RESEARCH_ITERATIONS", "3"))
)
```

**Integration**:

- Coordinates existing agents with agentic quality assessment
- Uses ADK-native LoopAgent and BaseAgent patterns
- No modification of existing agents
- Universal flexibility for any newsletter type

### Step 4: Comprehensive Testing & Documentation

**Unit Tests**:

- Test QualityAssessmentAgent escalation patterns
- Test LoopAgent coordination with agentic quality assessment
- Test error handling for missing inputs

**Integration Tests**:

- Test with diverse newsletter types (biotech VC, fintech, academic, daily news)
- Validate quality assessment adapts to user requirements using LLM intelligence
- Ensure existing agents work unchanged
- Verify no hardcoded return structures are used

**Documentation Updates**:

- Update `.docs/EXISTING_AGENTS.md` with agentic quality enhancement pattern
- Update `.docs/BUILDING_ORCHESTRATORS.md` with proper ADK-native examples
- Remove any remaining references to hardcoded dictionary returns

## Agentic Implementation Strategy

Based on Google ADK best practices and agentic principles:

1. **Custom BaseAgent Pattern**: Use QualityAssessmentAgent(BaseAgent) instead of tool functions
2. **Event-Based Escalation**: Use `Event(actions=EventActions(escalate=True))` for loop termination
3. **LLM Intelligence Trust**: Let LLM evaluate quality in natural language without predetermined structures
4. **Explicit Error Handling**: Raise clear ValueError exceptions for missing inputs

## Quality Assessment Logic (Agentic Approach)

**Core Principle**: Trust LLM intelligence completely - no hardcoded return structures

```python
class QualityAssessmentAgent(BaseAgent):
    """
    Agentic quality assessment that trusts LLM intelligence completely.

    This agent lets the LLM evaluate quality in natural language and uses
    simple pass/fail determination to escalate when quality is sufficient.
    No hardcoded dictionary structures or predetermined quality criteria.
    """

    async def _run_async_impl(self, ctx) -> AsyncGenerator[Event, None]:
        # Explicit error handling for missing inputs
        user_requirements = ctx.session.state.get("user_requirements", "")
        research_results = ctx.session.state.get("research_results", "")

        if not user_requirements:
            raise ValueError("No user requirements found for quality assessment")
        if not research_results:
            raise ValueError("No research results found for quality assessment")

        # Let LLM evaluate quality in natural language
        # (Implementation would use actual LLM call here)
        llm_assessment = "SUFFICIENT - comprehensive coverage meets user expectations..."

        # Simple pass/fail determination based on LLM's natural language response
        is_sufficient = llm_assessment.strip().upper().startswith("SUFFICIENT")

        # Save LLM's raw assessment and escalate if sufficient
        yield Event(
            author=self.name,
            actions=EventActions(
                state_delta={"quality_assessment": llm_assessment},
                escalate=is_sufficient
            )
        )
```

## Integration with Existing Agents

**Strategy**: Orchestration wrapper that enhances existing agent outputs without modification

**Key Benefits**:

- Leverage proven quality of existing WideSearchAgent and DeepSearchAgent
- Maintain backward compatibility
- No duplication of research capabilities
- Focus on orchestration, not rebuilding

## Environment Configuration

**Required Environment Variables**:

- `MAX_RESEARCH_ITERATIONS`: LoopAgent max iterations (default: 3)
- `MAX_QUALITY_ASSESSMENT_TOKENS`: LLM token limits for quality assessment
- `QUALITY_ASSESSMENT_TIMEOUT`: Timeout for quality evaluation

**Configuration Pattern**:

```python
max_iterations=int(os.getenv("MAX_RESEARCH_ITERATIONS", "3"))
```

## Validation Checkpoint Requirements

**Before Implementation, Validate**:

- [ ] Does quality assessment work for daily news newsletters?
- [ ] Does quality assessment work for biotech VC reports?
- [ ] Does quality assessment work for fintech regulatory updates?
- [ ] Does quality assessment work for academic research summaries?
- [ ] Does quality assessment adapt to user requirements (not hardcoded)?
- [ ] Does the agent follow ADK-native BaseAgent and Event escalation patterns?
- [ ] Does the implementation avoid hardcoded dictionary return structures?
- [ ] Does error handling validate all inputs and raise clear errors?
- [ ] Does the approach trust LLM intelligence completely?

## Success Criteria

**Day 3 Implementation Must Achieve**:

- ✅ Uses existing agents through orchestration (no duplication)
- ✅ Follows ADK-native LoopAgent escalation patterns
- ✅ Adapts quality criteria to user requirements (no hardcoding)
- ✅ Maintains universal flexibility across newsletter types
- ✅ Implements mandatory error handling with clear validation
- ✅ Follows agentic principles and ADK-native patterns

## Next Steps for Implementation

1. **Create QualityAssessmentAgent** with ADK-native BaseAgent and Event escalation
2. **Integrate with LoopAgent** coordinating existing agents
3. **Comprehensive Testing** across diverse newsletter types
4. **Update Documentation** with proper agentic patterns and examples
5. **Remove any remaining hardcoded dictionary structures** from codebase

## References

- `.docs/BUILDING_ORCHESTRATORS.md` - Implementation patterns and ADK guidance
- `.docs/EXISTING_AGENTS.md` - WideSearchAgent & DeepSearchAgent capabilities  
- `.docs/TROUBLESHOOTING.md` - Debugging patterns and error handling
- `.tasks/TODOS.md` - Current roadmap and acceptance criteria
- Google ADK Documentation - LoopAgent patterns and tool escalation
