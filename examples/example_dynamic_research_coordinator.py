"""
Test script for LLM-driven DynamicResearchCoordinator with diverse newsletter types.

This script validates that the DynamicResearchCoordinator uses LLM intelligence to
coordinate research for different newsletter types, following agentic principles
instead of programmatic logic.

To run this example from the root of the project:
`python -m examples.test_dynamic_research_coordinator`

You will need to have your EXA_API_KEY and GOOGLE_API_KEY environment variables set.
"""

import asyncio
import os
from dotenv import load_dotenv
from google.adk.runners import Runner
from google.genai import types

from underlines_adk.agents.dynamic_research_coordinator import DynamicResearchCoordinator
from underlines_adk.sessions import LoggedSessionService

# Load environment variables from a .env file if it exists
load_dotenv()

# Test cases for flexibility validation
TEST_CASES = [
    {
        "name": "Daily News Newsletter",
        "requirements": """
        I need a daily news newsletter covering global politics and technology with historical context.
        Target audience is busy professionals who want comprehensive coverage in 5-10 minutes of reading.
        Include 3 major stories with analysis of implications and background context.
        Focus on factual reporting with multiple perspectives.
        """,
        "research_plan": "Plan comprehensive research using both broad coverage and detailed analysis for current events."
    },
    {
        "name": "Biotech VC Report", 
        "requirements": """
        Weekly biotech newsletter focusing on TL1A research and clinical trials for investment analysis.
        Target audience is venture capital partners making investment decisions.
        Need comprehensive monitoring of companies, research developments, and competitive landscape.
        Include scientific accuracy, market implications, and regulatory considerations.
        """,
        "research_plan": "Plan specialized research focusing on TL1A developments with deep analysis of market implications."
    },
    {
        "name": "Fintech Regulatory Updates",
        "requirements": """
        Monthly fintech newsletter covering global banking innovation and regulatory changes.
        Target audience is fintech startup founders and compliance teams.
        Focus on regulatory updates that affect digital banking, payment systems, and financial technology.
        Include competitive analysis and technology developments in the space.
        """,
        "research_plan": "Plan regulatory-focused research with broad coverage of fintech developments and deep analysis of compliance impacts."
    },
]


async def test_dynamic_research_coordinator():
    """Test DynamicResearchCoordinator with diverse newsletter types."""
    
    # Verify environment variables
    if not os.getenv("EXA_API_KEY"):
        print("Error: EXA_API_KEY environment variable not set")
        return
    
    if not os.getenv("GOOGLE_API_KEY"):
        print("Error: GOOGLE_API_KEY environment variable not set")
        return

    print("🧪 Testing DynamicResearchCoordinator")
    print("=" * 50)
    
    # Create session service with logging
    session_service = LoggedSessionService()

    for i, test_case in enumerate(TEST_CASES, 1):
        print(f"\n📋 Test {i}: {test_case['name']}")
        print("-" * 30)
        
        # Create LLM-driven DynamicResearchCoordinator instance
        coordinator = DynamicResearchCoordinator(
            name="LLMDrivenResearchCoordinator",
            description="Uses LLM intelligence to coordinate research using existing agents"
        )
        
        # Create runner with DynamicResearchCoordinator
        runner = Runner(
            agent=coordinator,
            app_name="dynamic_research_coordinator_test",
            session_service=session_service,
        )
        
        # Create new session for each test
        session = await runner.session_service.create_session(
            app_name=runner.app_name, user_id="test_user"
        )
        session_id = session.id
        print(f"Session ID: {session_id}")
        
        # Prepare user content with requirements (coordinator will extract from message)
        user_content = types.Content(
            role="user",
            parts=[types.Part(text=test_case["requirements"])]
        )

        # Also set up session state for additional context
        session.state["research_plan"] = test_case["research_plan"]
        
        print("📝 User Requirements:")
        print(test_case["requirements"].strip())
        print(f"\n📋 Research Plan: {test_case['research_plan']}")
        print("\n🔄 Running LLM-driven DynamicResearchCoordinator...")
        
        try:
            # Run the dynamic research coordinator
            final_response = None
            event_count = 0
            async for event in runner.run_async(
                user_id="test_user",
                session_id=session_id,
                new_message=user_content,
            ):
                event_count += 1
                if event.is_final_response() and event.content:
                    final_response = event.content.parts[0].text
                elif event.content:
                    # Print intermediate results from coordinated agents
                    content_preview = event.content.parts[0].text[:200] + "..." if len(event.content.parts[0].text) > 200 else event.content.parts[0].text
                    print(f"  📊 Event from {event.author}: {content_preview}")
            
            if final_response:
                print("✅ Research Coordination Complete:")
                print(final_response[:500] + "..." if len(final_response) > 500 else final_response)
                print(f"\n📊 Full response length: {len(final_response)} characters")
                print(f"📊 Total events processed: {event_count}")
            else:
                print("❌ No final response generated")
                print(f"📊 Total events processed: {event_count}")
                
        except Exception as e:
            print(f"❌ Error: {str(e)}")
        
        print("\n" + "="*50)
    
    print("\n🎯 LLM-driven Research Coordination Validation Complete")
    print("All test cases demonstrate LLM intelligence coordinating existing agents without programmatic logic.")


async def test_llm_driven_coordination_principles():
    """Test that the coordinator follows LLM-driven principles."""
    print("\n🧠 Testing LLM-driven Coordination Principles")
    print("-" * 30)

    coordinator = DynamicResearchCoordinator()

    # Verify no hardcoded programmatic logic
    assert not hasattr(coordinator, '_determine_research_areas_needed'), "Should not have hardcoded area determination"
    assert not hasattr(coordinator, '_create_research_workflows_from_requirements'), "Should not have programmatic workflow creation"

    # Verify LLM-driven approach
    assert hasattr(coordinator, '_create_llm_driven_coordination_strategy'), "Should have LLM-driven strategy creation"

    print("✅ LLM-driven principles validated:")
    print("   - No hardcoded keyword matching logic")
    print("   - No programmatic decision trees")
    print("   - Uses LLM intelligence for coordination decisions")
    print("   - Maintains universal flexibility through LLM understanding")


if __name__ == "__main__":
    asyncio.run(test_dynamic_research_coordinator())
    asyncio.run(test_llm_driven_coordination_principles())
