#!/usr/bin/env python3
"""
Test script for agentic quality assessment implementation.

This script demonstrates and tests the new QualityAssessmentAgent following
agentic principles. It validates that the implementation:

1. Uses LLM intelligence for quality evaluation
2. Follows ADK-native Event escalation patterns
3. Avoids hardcoded dictionary return structures
4. Provides explicit error handling for missing inputs
5. Works with diverse newsletter types

Usage:
    python examples/test_agentic_quality_assessment.py
"""

import asyncio
import os
from google.adk.runners import Runner
from google.adk.agents import LoopAgent
from google.genai import types

from underlines_adk.sessions import LoggedSessionService
from underlines_adk.agents.quality_assessment_agent import QualityAssessmentAgent
from underlines_adk.agents.wide_search_agent import exa_agent as wide_search_agent
from underlines_adk.agents.deep_search_agent import exa_agent as deep_search_agent


async def test_quality_assessment_agent():
    """Test QualityAssessmentAgent with various scenarios."""
    print("🧪 Testing QualityAssessmentAgent...")
    
    # Create session service
    session_service = LoggedSessionService()
    
    # Test 1: Error handling for missing inputs
    print("\n📋 Test 1: Error handling for missing inputs")
    quality_agent = QualityAssessmentAgent(
        name="TestQualityAgent",
        description="Test agent for validation"
    )
    
    runner = Runner(
        agent=quality_agent,
        app_name="quality_test",
        session_service=session_service
    )
    
    # Create session with empty state (should trigger errors)
    session = await session_service.create_session(
        app_name="quality_test",
        user_id="test_user",
        state={}  # Empty state - should trigger ValueError
    )
    
    try:
        user_content = types.Content(parts=[types.Part(text="Test quality assessment")])

        events = []
        async for event in runner.run_async(
            user_id="test_user",
            session_id=session.id,
            new_message=user_content
        ):
            events.append(event)
            if event.is_final_response():
                break
        
        print("❌ Expected ValueError for missing inputs, but agent completed")
        
    except Exception as e:
        if "No user requirements found" in str(e) or "No research results found" in str(e):
            print("✅ Correctly raised ValueError for missing inputs")
        else:
            print(f"❌ Unexpected error: {e}")
    
    # Test 2: Quality assessment with sufficient research
    print("\n📋 Test 2: Quality assessment with sufficient research")
    
    # Create session with proper inputs
    session_sufficient = await session_service.create_session(
        app_name="quality_test",
        user_id="test_user_2",
        state={
            "user_requirements": """
            I need a weekly biotech newsletter focusing on TL1A research and clinical trials.
            Target audience: biotech VCs and investment professionals.
            Depth: Comprehensive analysis with market implications.
            Sources: Authoritative scientific journals and industry reports.
            """,
            "research_results": """
            Comprehensive TL1A Research Analysis:
            
            1. Recent Clinical Trials:
            - Phase II trial by Prometheus Biosciences showing 70% response rate
            - Roche's anti-TL1A antibody entering Phase III for IBD
            - Multiple biotech companies advancing TL1A-targeted therapies
            
            2. Market Analysis:
            - $2.5B market opportunity by 2030
            - 15+ companies in development pipeline
            - Strong IP landscape with key patents expiring 2027-2029
            
            3. Investment Implications:
            - Prometheus acquisition by Roche for $10.8B validates target
            - Emerging companies: Teva, AbbVie, Janssen pursuing programs
            - Risk factors: competitive landscape, regulatory pathways
            
            Sources: Nature Medicine, BioPharma Dive, PharmaIntelligence, SEC filings
            """
        }
    )
    
    try:
        user_content = types.Content(parts=[types.Part(text="Test quality assessment")])

        events = []
        async for event in runner.run_async(
            user_id="test_user_2",
            session_id=session_sufficient.id,
            new_message=user_content
        ):
            events.append(event)
            if event.is_final_response():
                break
        
        # Check if quality assessment was saved to state
        final_session = await session_service.get_session(
            app_name="quality_test",
            user_id="test_user_2",
            session_id=session_sufficient.id
        )
        
        quality_assessment = final_session.state.get("quality_assessment", "")
        if quality_assessment:
            print(f"✅ Quality assessment completed: {quality_assessment[:100]}...")
            
            # Check if assessment follows agentic principles (starts with SUFFICIENT/NEEDS_IMPROVEMENT)
            if quality_assessment.upper().startswith(("SUFFICIENT", "NEEDS_IMPROVEMENT")):
                print("✅ Assessment follows agentic format (SUFFICIENT/NEEDS_IMPROVEMENT)")
            else:
                print("❌ Assessment doesn't follow expected agentic format")
        else:
            print("❌ No quality assessment found in session state")
            
    except Exception as e:
        print(f"❌ Quality assessment failed: {e}")


async def test_context_propagation():
    """Test context propagation to existing agents in LoopAgent configuration."""
    print("\n🔄 Testing context propagation to existing agents...")

    from underlines_adk.agents.dynamic_research_coordinator import DynamicResearchCoordinator

    # Create coordinator that handles context propagation
    coordinator = DynamicResearchCoordinator()

    # Test context-aware research loop creation
    user_requirements = """
    Weekly biotech newsletter for VC investors.
    Focus: TL1A therapeutic development and market opportunities.
    Depth: Investment-grade analysis with risk assessment.
    Target audience: Biotech VCs and investment professionals.
    """

    research_plan = """
    Research Plan:
    1. Identify key TL1A programs in development
    2. Analyze competitive landscape and IP positions
    3. Assess market opportunity and investment implications
    4. Evaluate regulatory pathways and timelines
    """

    # Create quality-enhanced research loop using ADK-native patterns
    research_loop = coordinator.create_quality_enhanced_research_loop(
        area_name="TL1A_therapeutics"
    )

    print(f"✅ Created research loop: {research_loop.name}")
    print(f"✅ Sub-agents: {[agent.name for agent in research_loop.sub_agents]}")

    # Verify context-aware agents were created
    context_aware_agents = [agent for agent in research_loop.sub_agents if "ContextAware" in agent.name]
    if len(context_aware_agents) >= 2:
        print("✅ Context-aware agent wrappers created successfully")
    else:
        print("❌ Context-aware agent wrappers not found")


async def test_quality_enhanced_loop():
    """Test QualityAssessmentAgent in LoopAgent configuration with existing agents."""
    print("\n🔄 Testing QualityAssessmentAgent in LoopAgent with existing agents...")

    # Use existing proven agents with context propagation
    
    # Create quality assessment agent
    quality_agent = QualityAssessmentAgent(
        name="TestQualityAssessor",
        description="Test quality assessment in loop"
    )
    
    # Create LoopAgent with existing agents and quality assessment
    loop_agent = LoopAgent(
        name="QualityEnhancedResearchLoop",
        sub_agents=[wide_search_agent, deep_search_agent, quality_agent],
        max_iterations=3
    )
    
    # Create session service and runner
    session_service = LoggedSessionService()
    runner = Runner(
        agent=loop_agent,
        app_name="loop_test",
        session_service=session_service
    )
    
    # Create session with user requirements
    session = await session_service.create_session(
        app_name="loop_test",
        user_id="loop_test_user",
        state={
            "user_requirements": """
            Weekly biotech newsletter for VC investors.
            Focus: TL1A therapeutic development and market opportunities.
            Depth: Investment-grade analysis with risk assessment.
            """
        }
    )
    
    try:
        user_content = types.Content(parts=[types.Part(text="Generate research with quality assessment")])
        
        iteration_count = 0
        events = []
        
        async for event in runner.run_async(
            user_id="loop_test_user",
            session_id=session.id,
            new_message=user_content
        ):
            events.append(event)
            
            # Count iterations by checking for escalation events
            if event.actions and event.actions.escalate:
                print(f"✅ Quality assessment escalated (loop terminated)")
                break
                
            if event.is_final_response():
                iteration_count += 1
                if iteration_count >= 3:
                    print("⚠️  Loop completed maximum iterations without escalation")
                break
        
        # Check final state
        final_session = await session_service.get_session(
            app_name="loop_test",
            user_id="loop_test_user",
            session_id=session.id
        )
        
        research_results = final_session.state.get("research_results", "")
        quality_assessment = final_session.state.get("quality_assessment", "")
        
        if research_results and quality_assessment:
            print("✅ LoopAgent successfully coordinated research and quality assessment")
        else:
            print("❌ LoopAgent coordination incomplete")
            
    except Exception as e:
        print(f"❌ LoopAgent test failed: {e}")


async def main():
    """Run all quality assessment tests."""
    print("🚀 Starting Agentic Quality Assessment Tests")
    print("=" * 60)

    await test_quality_assessment_agent()
    await test_context_propagation()
    await test_quality_enhanced_loop()

    print("\n" + "=" * 60)
    print("✅ Agentic Quality Assessment Tests Complete")


if __name__ == "__main__":
    asyncio.run(main())
