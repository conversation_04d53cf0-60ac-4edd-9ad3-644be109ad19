# Examples

<!-- purpose-start -->
This directory contains practical examples demonstrating how to use the Underlines ADK components. Each example showcases different features and usage patterns, from basic agent usage to advanced session management and logging.
<!-- purpose-end -->

<!-- architecture-start -->
## Architecture

Examples are designed to be self-contained demonstrations that can be run independently:

```
examples/
├── run_wide_search.py    # Complete agent usage with logging
└── README.md            # This documentation

Each example:
- Imports required components
- Sets up necessary services
- Demonstrates specific functionality
- Includes error handling and logging
```
<!-- architecture-end -->

<!-- contents-start -->
## Contents

### Available Examples

- **`run_wide_search.py`**: Complete example of using WideSearchAgent
  - Demonstrates LoggedSessionService usage
  - Shows proper session management
  - Includes environment variable handling
  - Provides comprehensive logging output
  - Handles API key validation

- **`run_deep_search.py`**: Complete example of using DeepSearchAgent
  - Demonstrates in-depth topic analysis with citations
  - Shows detailed logging and event tracking
  - Includes comprehensive error handling
  - Provides response preview functionality
  - Handles multiple API key configurations (OpenAI/Gemini)

### Example Categories

**Basic Usage**:
- Agent setup and configuration
- Session creation and management
- Simple search operations

**Advanced Features**:
- Automatic event logging
- Error handling and recovery
- Environment configuration
- Async operation patterns
<!-- contents-end -->

## Running Examples

### Prerequisites

1. **Install Dependencies**:
```bash
# Using Poetry (recommended)
poetry install

# Or using pip
pip install -e .
```

2. **Set Environment Variables**:
```bash
# Required for Exa API access
export EXA_API_KEY="your_exa_api_key_here"

# Or create a .env file
echo "EXA_API_KEY=your_key" > .env
```

3. **Verify Setup**:
```bash
# Test that imports work
python -c "from underlines_adk.agents.wide_search_agent import exa_agent; print('Setup OK')"
```

### Running Individual Examples

**Wide Search Example**:
```bash
# From project root
python -m examples.run_wide_search

# Or directly
cd examples
python run_wide_search.py
```

**Deep Search Example**:
```bash
# From project root
python -m examples.run_deep_search

# Or directly
cd examples
python run_deep_search.py
```

**Expected Output**:
```
Initializing runner with LoggedSessionService...
Session created with ID: session_abc123
Logs will be saved to: session_logs/session_abc123.jsonl

Running agent...
Agent finished with a final response.
Final response length: 1234 characters.

✅ Run complete. Check the log file at: session_logs/session_abc123.jsonl
```

## Example Walkthrough

### run_wide_search.py Detailed Analysis

This example demonstrates a complete workflow:

1. **Environment Setup**:
```python
from dotenv import load_dotenv
load_dotenv()  # Load .env file if present

if not os.getenv("EXA_API_KEY"):
    print("Please set the EXA_API_KEY environment variable")
    return
```

2. **Service Configuration**:
```python
# Create session service with automatic logging
session_service = LoggedSessionService()

# Create runner with agent and session service
runner = Runner(
    agent=exa_agent,
    app_name="wide_search_example",
    session_service=session_service,
)
```

3. **Session Management**:
```python
# Create new session
session = await runner.session_service.create_session(
    app_name=runner.app_name, 
    user_id="example_user"
)

print(f"Session created with ID: {session.id}")
print(f"Logs will be saved to: session_logs/{session.id}.jsonl")
```

4. **Agent Execution**:
```python
# Prepare user request
user_content = types.Content(
    role="user",
    parts=[types.Part(text='{"query": "latest news in generative AI", "lookback_days": 2}')]
)

# Run agent and process events
async for event in runner.run_async(
    user_id="example_user",
    session_id=session.id,
    new_message=user_content,
):
    if event.is_final_response() and event.content:
        final_response_content = event.content.parts[0].text
        print("Agent finished with a final response.")
```

5. **Result Processing**:
```python
if final_response_content:
    print(f"Final response length: {len(final_response_content)} characters.")
else:
    print("Agent did not produce a final response.")

print(f"✅ Run complete. Check the log file at: session_logs/{session.id}.jsonl")
```

## Creating New Examples

### Example Template

```python
"""
Example: [Brief description of what this example demonstrates]

This example shows how to [specific functionality].

To run this example from the root of the project:
`python -m examples.example_name`

Requirements:
- [List any required environment variables]
- [List any special setup requirements]
"""

import asyncio
import os
from dotenv import load_dotenv

# Import required components
from underlines_adk.agents.wide_search_agent import exa_agent
from underlines_adk.sessions import LoggedSessionService
from google.adk.runners import Runner
from google.genai import types

# Load environment variables
load_dotenv()

async def main():
    """Main example function with clear documentation."""
    
    # 1. Validate prerequisites
    if not os.getenv("REQUIRED_API_KEY"):
        print("Please set the REQUIRED_API_KEY environment variable.")
        return
    
    print("Starting example...")
    
    # 2. Set up services
    session_service = LoggedSessionService()
    runner = Runner(
        agent=exa_agent,
        app_name="example_app",
        session_service=session_service
    )
    
    # 3. Create session
    session = await session_service.create_session(
        app_name="example_app",
        user_id="example_user"
    )
    
    print(f"Session created: {session.id}")
    
    # 4. Demonstrate specific functionality
    # [Your example-specific code here]
    
    # 5. Show results
    print("✅ Example completed successfully")

if __name__ == "__main__":
    asyncio.run(main())
```

### Best Practices for Examples

**Clear Documentation**:
- Explain what the example demonstrates
- List all prerequisites and requirements
- Provide step-by-step comments
- Show expected output

**Error Handling**:
```python
async def robust_example():
    """Example with comprehensive error handling"""
    try:
        # Main example logic
        result = await run_example_logic()
        print(f"Success: {result}")
        
    except KeyError as e:
        print(f"Missing required environment variable: {e}")
        print("Please check your .env file or environment setup")
        
    except Exception as e:
        print(f"Example failed with error: {e}")
        print("Check the logs for more details")
        import traceback
        traceback.print_exc()
```

**Resource Cleanup**:
```python
async def clean_example():
    """Example with proper resource cleanup"""
    session_service = None
    session = None
    
    try:
        session_service = LoggedSessionService()
        session = await session_service.create_session(...)
        
        # Main example logic
        await run_example(session)
        
    finally:
        # Cleanup resources
        if session and session_service:
            await session_service.delete_session(session_id=session.id)
            print("Session cleaned up")
```

## Advanced Examples

### Custom Agent Example

```python
"""
Example: Creating and using a custom agent

This example demonstrates how to create a custom agent with specific tools and behavior.
"""

from google.adk.agents.llm_agent import LlmAgent
from underlines_adk.tools.exa_tools import exa_deep_search
from underlines_adk.tools.litellm_tools import llm

# Create custom agent
custom_agent = LlmAgent(
    name="CustomSearchAgent",
    model=llm,
    description="Performs focused searches with detailed analysis",
    instruction="""
    Use exa_deep_search to find detailed information about the requested topic.
    Analyze the full text content and provide:
    1. Key insights
    2. Supporting evidence
    3. Relevant quotes
    4. Source credibility assessment
    
    Format your response as a structured analysis report.
    """,
    tools=[exa_deep_search],
    output_key="detailed_analysis",
)

async def custom_agent_example():
    """Demonstrate custom agent usage"""
    # Use custom agent with standard infrastructure
    session_service = LoggedSessionService()
    runner = Runner(
        agent=custom_agent,  # Use our custom agent
        app_name="custom_agent_example",
        session_service=session_service
    )
    
    # Rest of the example follows standard pattern
    # ...
```

### Multiple Agents Example

```python
"""
Example: Using multiple agents in sequence

This example shows how to use different agents for different tasks.
"""

from underlines_adk.agents.wide_search_agent import exa_agent as wide_agent
# from underlines_adk.agents.deep_search_agent import deep_agent  # When implemented

async def multi_agent_example():
    """Demonstrate using multiple agents"""
    session_service = LoggedSessionService()
    
    # First agent: Wide search
    wide_runner = Runner(
        agent=wide_agent,
        app_name="multi_agent_wide",
        session_service=session_service
    )
    
    session = await session_service.create_session(
        app_name="multi_agent_wide",
        user_id="multi_user"
    )
    
    # Run wide search
    wide_query = types.Content(
        role="user",
        parts=[types.Part(text='{"query": "AI research", "lookback_days": 7}')]
    )
    
    wide_results = None
    async for event in wide_runner.run_async(
        user_id="multi_user",
        session_id=session.id,
        new_message=wide_query
    ):
        if event.is_final_response():
            wide_results = event.content.parts[0].text
    
    print(f"Wide search completed: {len(wide_results)} characters")
    
    # Could continue with deep search agent for specific topics
    # identified in the wide search...
```

### Callback Example

```python
"""
Example: Using callbacks for event processing

This example demonstrates callback-based event handling.
"""

from underlines_adk.callbacks.json_logger import log_event_to_json

def custom_callback(event, session):
    """Custom callback for demonstration"""
    if event.is_final_response():
        print(f"🎉 Final response in session {session.id}")
    elif event.error_code:
        print(f"❌ Error in session {session.id}: {event.error_message}")

async def callback_example():
    """Demonstrate callback usage"""
    session_service = InMemorySessionService()  # No LoggedSessionService needed
    
    runner = Runner(
        agent=exa_agent,
        app_name="callback_example",
        session_service=session_service,
        callbacks=[
            log_event_to_json,  # JSON logging via callback
            custom_callback     # Custom processing
        ]
    )
    
    # Use runner normally - callbacks will process events
    # ...
```

<!-- test-refs-start -->
## Testing Examples

### Manual Testing
```bash
# Test each example individually
python -m examples.run_wide_search

# Test with different environment configurations
EXA_API_KEY=test_key python -m examples.run_wide_search
```

### Automated Testing
```bash
# Run example tests (if implemented)
pytest tests/examples/

# Test example imports
python -c "import examples.run_wide_search; print('Import OK')"
```
<!-- test-refs-end -->

<!-- doc-refs-start -->
## Documentation

- **[Main README](../README.md)**: Project overview and setup
- **[Agent Documentation](../underlines_adk/agents/README.md)**: How to use agents
- **[Session Documentation](../underlines_adk/sessions/README.md)**: Session management
- **[Logging Guide](../.docs/LOGGING.md)**: Understanding the logging system
- **[Agent Patterns](../.docs/AGENT_PATTERNS.md)**: Agent development guide
- **[Workflows Guide](../.docs/WORKFLOWS.md)**: Development, usage, and maintenance workflows
<!-- doc-refs-end -->

<!-- dependent-refs-start -->
## Dependencies

### External Dependencies
- **All project dependencies**: Examples use the full Underlines ADK
- **python-dotenv**: For .env file support
- **Environment variables**: API keys for external services

### Internal Dependencies
- **underlines_adk.agents**: For agent implementations
- **underlines_adk.sessions**: For session management
- **underlines_adk.tools**: Indirectly through agents
- **google-adk**: For runners and core functionality
<!-- dependent-refs-end -->

<!-- troubleshooting-start -->
## Troubleshooting

### Common Issues

**Import Errors**:
```bash
# Ensure package is installed
poetry install
# Or
pip install -e .

# Test imports
python -c "from underlines_adk.agents.wide_search_agent import exa_agent"
```

**Missing API Keys**:
```bash
# Check environment variables
echo $EXA_API_KEY

# Create .env file
echo "EXA_API_KEY=your_key_here" > .env
```

**Module Not Found**:
```bash
# Run from project root
python -m examples.run_wide_search

# Not from examples directory
cd examples
python run_wide_search.py  # This might not work
```

### Example-Specific Issues

**run_wide_search.py**:
- Requires valid EXA_API_KEY
- Creates session_logs/ directory
- May take 30-60 seconds to complete
- Check network connectivity for API calls

**Log Files Not Created**:
- Check write permissions in current directory
- Verify session_logs/ directory creation
- Check disk space availability

### Development Issues

**Creating New Examples**:
1. Follow the template structure
2. Include comprehensive error handling
3. Add clear documentation
4. Test with various configurations
5. Consider edge cases and failures

**Testing Examples**:
```python
# Test example functions directly
from examples.run_wide_search import main

# Run with test configuration
import asyncio
asyncio.run(main())
```
<!-- troubleshooting-end -->
