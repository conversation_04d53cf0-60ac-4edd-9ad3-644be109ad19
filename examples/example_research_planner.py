"""
Test script for ResearchPlanner orchestrator with diverse newsletter types.

This script validates that the ResearchPlanner works with different newsletter types
as required by the flexibility validation checklist:
- Daily news newsletters
- Biotech VC reports  
- Fintech regulatory updates
- Academic research summaries

To run this example from the root of the project:
`python -m examples.test_research_planner`

You will need to have your EXA_API_KEY and GOOGLE_API_KEY environment variables set.
"""

import asyncio
import os
from dotenv import load_dotenv
from google.adk.runners import Runner
from google.genai import types

from underlines_adk.agents.research_planner import research_planner
from underlines_adk.sessions import LoggedSessionService

# Load environment variables from a .env file if it exists
load_dotenv()

# Test cases for flexibility validation
TEST_CASES = [
    {
        "name": "Daily News Newsletter",
        "requirements": """
        I need a daily news newsletter covering global politics and technology with historical context.
        Target audience is busy professionals who want comprehensive coverage in 5-10 minutes of reading.
        Include 3 major stories with analysis of implications and background context.
        Focus on factual reporting with multiple perspectives.
        """,
    },
    {
        "name": "Biotech VC Report", 
        "requirements": """
        Weekly biotech newsletter focusing on TL1A research and clinical trials for investment analysis.
        Target audience is venture capital partners making investment decisions.
        Need comprehensive monitoring of companies, research developments, and competitive landscape.
        Include scientific accuracy, market implications, and regulatory considerations.
        """,
    },
    {
        "name": "Fintech Regulatory Updates",
        "requirements": """
        Monthly fintech newsletter covering global banking innovation and regulatory changes.
        Target audience is fintech startup founders and compliance teams.
        Focus on regulatory updates that affect digital banking, payment systems, and financial technology.
        Include competitive analysis and technology developments in the space.
        """,
    },
    {
        "name": "Academic Research Summary",
        "requirements": """
        Quarterly machine learning research newsletter for academic researchers.
        Target audience is PhD students and faculty in computer science departments.
        Focus on breakthrough research papers, methodology innovations, and theoretical advances.
        Include detailed analysis of research methodologies and implications for the field.
        """,
    },
]


async def test_research_planner_flexibility():
    """Test ResearchPlanner with diverse newsletter types."""
    
    # Verify environment variables
    if not os.getenv("EXA_API_KEY"):
        print("Error: EXA_API_KEY environment variable not set")
        return
    
    if not os.getenv("GOOGLE_API_KEY"):
        print("Error: GOOGLE_API_KEY environment variable not set")
        return

    print("🧪 Testing ResearchPlanner Flexibility")
    print("=" * 50)
    
    # Create session service with logging
    session_service = LoggedSessionService()
    
    # Create runner with ResearchPlanner
    runner = Runner(
        agent=research_planner,
        app_name="research_planner_test",
        session_service=session_service,
    )

    for i, test_case in enumerate(TEST_CASES, 1):
        print(f"\n📋 Test {i}: {test_case['name']}")
        print("-" * 30)
        
        # Create new session for each test
        session = await runner.session_service.create_session(
            app_name=runner.app_name, user_id="test_user"
        )
        session_id = session.id
        print(f"Session ID: {session_id}")
        
        # Prepare user requirements
        user_content = types.Content(
            role="user",
            parts=[types.Part(text=test_case["requirements"])]
        )
        
        print("📝 User Requirements:")
        print(test_case["requirements"].strip())
        print("\n🔄 Running ResearchPlanner...")
        
        try:
            # Run the research planner
            final_response = None
            async for event in runner.run_async(
                user_id="test_user",
                session_id=session_id,
                new_message=user_content,
            ):
                if event.is_final_response() and event.content:
                    final_response = event.content.parts[0].text
                    break
            
            if final_response:
                print("✅ Research Plan Generated:")
                print(final_response[:500] + "..." if len(final_response) > 500 else final_response)
                print(f"\n📊 Full response length: {len(final_response)} characters")
            else:
                print("❌ No response generated")
                
        except Exception as e:
            print(f"❌ Error: {str(e)}")
        
        print("\n" + "="*50)
    
    print("\n🎯 Flexibility Validation Complete")
    print("All test cases represent different newsletter types that the ResearchPlanner must handle.")


if __name__ == "__main__":
    asyncio.run(test_research_planner_flexibility())
