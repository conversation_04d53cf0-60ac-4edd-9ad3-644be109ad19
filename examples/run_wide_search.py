"""
Example of running the WideSearchAgent with session logging enabled.

This script demonstrates how to use the `LoggedSessionService` to wrap
an existing session service and automatically log all events.

To run this example from the root of the project:
`python -m examples.run_wide_search`

You will need to have your EXA_API_KEY environment variable set.
"""

import asyncio
import os

from dotenv import load_dotenv
from google.adk.runners import Runner
from google.genai import types

from underlines_adk.agents.wide_search_agent import exa_agent
from underlines_adk.sessions import LoggedSessionService

# Load environment variables from a .env file if it exists
load_dotenv()


async def main():
    """Set up and run the agent, then print log file info."""
    if not os.getenv("EXA_API_KEY"):
        print("Please set the EXA_API_KEY environment variable to run this example.")
        return

    print("Initializing runner with LoggedSessionService...")
    # 1. Create the session service that automatically logs events.
    #    By default, it uses an InMemorySessionService underneath.
    session_service = LoggedSessionService()

    # 2. Create the runner with our logging-enabled service.
    runner = Runner(
        agent=exa_agent,
        app_name="wide_search_example",
        session_service=session_service,
    )

    print("Creating a new session...")
    session = await runner.session_service.create_session(
        app_name=runner.app_name, user_id="example_user"
    )
    session_id = session.id
    print(f"Session created with ID: {session_id}")
    print(f"Logs will be saved to: session_logs/{session_id}.jsonl\n")

    # Prepare the user's request
    user_content = types.Content(
        role="user",
        parts=[
            types.Part(
                text='{"query": "latest news in generative AI", "lookback_days": 2}'
            )
        ],
    )

    print("Running agent...")
    final_response_content = None
    # 3. Run the agent. Events will be logged automatically by the service.
    async for event in runner.run_async(
        user_id="example_user",
        session_id=session_id,
        new_message=user_content,
    ):
        if event.is_final_response() and event.content:
            final_response_content = event.content.parts[0].text
            print("Agent finished with a final response.")

    if final_response_content:
        print(f"Final response length: {len(final_response_content)} characters.")
    else:
        print("Agent did not produce a final response.")

    print(f"\n✅ Run complete. Check the log file at: session_logs/{session_id}.jsonl")


if __name__ == "__main__":
    asyncio.run(main())
