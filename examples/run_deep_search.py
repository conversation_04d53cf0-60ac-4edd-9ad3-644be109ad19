"""
Example of running the DeepSearchAgent with session logging enabled.

This script demonstrates how to use the `LoggedSessionService` to wrap
an existing session service and automatically log all events.

To run this example from the root of the project:
`python -m examples.run_deep_search`

You will need to have your EXA_API_KEY environment variable set.
"""

import asyncio
import logging
import os

from dotenv import load_dotenv
from google.adk.runners import Runner
from google.genai import types

from underlines_adk.agents.deep_search_agent import exa_agent
from underlines_adk.sessions import LoggedSessionService

# Load environment variables from a .env file if it exists
load_dotenv()

# Set up detailed logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def main():
    """Set up and run the agent, then print log file info."""
    logger.info("Starting deep search agent example")

    # Check for required environment variables
    exa_key = os.getenv("EXA_API_KEY")
    openai_key = os.getenv("OPENAI_API_KEY")
    gemini_key = os.getenv("GOOGLE_API_KEY")

    logger.info(f"EXA_API_KEY present: {bool(exa_key)}")
    logger.info(f"OPENAI_API_KEY present: {bool(openai_key or gemini_key)}")

    if not exa_key:
        print("Please set the EXA_API_KEY environment variable to run this example.")
        return

    if not openai_key and not gemini_key:
        print("Please set the OPENAI_API_KEY or GOOGLE_API_KEY environment variable to run this example.")
        return

    logger.info("Initializing runner with LoggedSessionService...")
    print("Initializing runner with LoggedSessionService...")

    # 1. Create the session service that automatically logs events.
    #    By default, it uses an InMemorySessionService underneath.
    session_service = LoggedSessionService()
    logger.info("LoggedSessionService created")

    # 2. Create the runner with our logging-enabled service.
    logger.info("Creating runner with deep search agent")
    runner = Runner(
        agent=exa_agent,
        app_name="deep_search_example",
        session_service=session_service,
    )
    logger.info("Runner created successfully")

    logger.info("Creating a new session...")
    print("Creating a new session...")
    session = await runner.session_service.create_session(
        app_name=runner.app_name, user_id="example_user"
    )
    session_id = session.id
    logger.info(f"Session created with ID: {session_id}")
    print(f"Session created with ID: {session_id}")
    print(f"Logs will be saved to: session_logs/{session_id}.jsonl\n")

    # Prepare the user's request for deep analysis of a specific story/topic
    query_text = '{"query": "Google stock price trends", "lookback_days": 7}'
    logger.info(f"Preparing user query: {query_text}")

    user_content = types.Content(
        role="user",
        parts=[
            types.Part(text=query_text)
        ],
    )
    logger.info("User content prepared")

    logger.info("Starting agent execution...")
    print("Running deep search agent...")

    final_response_content = None
    event_count = 0

    # 3. Run the agent. Events will be logged automatically by the service.
    try:
        async for event in runner.run_async(
            user_id="example_user",
            session_id=session_id,
            new_message=user_content,
        ):
            event_count += 1
            logger.info(f"Received event #{event_count}: {type(event).__name__}")

            if hasattr(event, 'content') and event.content:
                logger.info(f"Event has content with {len(event.content.parts)} parts")

            if event.is_final_response():
                logger.info("Received final response event")
                if event.content:
                    final_response_content = event.content.parts[0].text
                    logger.info(f"Final response length: {len(final_response_content)} characters")
                    print("Agent finished with a final response.")
                else:
                    logger.warning("Final response event has no content")

    except Exception as e:
        logger.error(f"Error during agent execution: {e}", exc_info=True)
        print(f"Error during agent execution: {e}")
        return
    finally:
        # Close any pending aiohttp sessions
        import aiohttp
        for task in asyncio.all_tasks():
            if not task.done() and "aiohttp" in str(task):
                task.cancel()
        
        # Allow time for connections to close
        await asyncio.sleep(0.250)

    logger.info(f"Agent execution completed. Total events: {event_count}")

    if final_response_content:
        print(f"Final response length: {len(final_response_content)} characters.")
        print("\n" + "="*50)
        print("DEEP SEARCH ANALYSIS PREVIEW:")
        print("="*50)
        # Show first 500 characters of the response
        preview = final_response_content[:500]
        if len(final_response_content) > 500:
            preview += "..."
        print(preview)
        print("="*50)
    else:
        logger.warning("No final response content received")
        print("Agent did not produce a final response.")

    print(f"\n✅ Run complete. Check the log file at: session_logs/{session_id}.jsonl")
    logger.info("Deep search agent example completed")


if __name__ == "__main__":
    asyncio.run(main())
