"""
Callbacks for the Underlines ADK project.

This module provides callback-based event handling for the Underlines ADK,
offering an alternative approach to event logging through Google ADK's callback
system. It enables event processing and logging without modifying session
service implementations.

Key Components:
    - json_logger: Callback-based JSON logging implementation

Features:
    - Callback-based event processing using Google ADK's native system
    - Non-intrusive logging without session service modification
    - Event serialization to JSON Lines files
    - Session isolation with separate log files
    - Error resilience and graceful failure handling

Usage:
    from underlines_adk.callbacks.json_logger import log_event_to_json
    from google.adk.runners import Runner

    # Register callback with runner
    runner = Runner(
        agent=my_agent,
        callbacks=[log_event_to_json]
    )

Architecture:
    Uses Google ADK's callback mechanism to intercept and process events.
    Provides an alternative to the LoggedSessionService decorator pattern,
    allowing logging to be added through callback registration.

Comparison with LoggedSessionService:
    - Callbacks: More flexible, selective processing, multiple processors
    - LoggedSessionService: Simpler setup, guaranteed logging, transparent

Dependencies:
    - google.adk.events: Event objects for processing
    - google.adk.sessions: Session objects for context
    - Standard library: json, logging, datetime, pathlib
"""
