# Callbacks Module

<!-- purpose-start -->
This module provides callback-based event handling for the Underlines ADK, offering an alternative approach to event logging through Google ADK's callback system. It enables event processing and logging without modifying session service implementations.
<!-- purpose-end -->

<!-- architecture-start -->
## Architecture

The callbacks module uses Google ADK's callback mechanism to intercept and process events:

```
┌─────────────────────┐
│   ADK Runner        │
│                     │
└─────────┬───────────┘
          │ events
┌─────────▼───────────┐
│   Callback System   │ ◄─── Google ADK built-in
│                     │
└─────────┬───────────┘
          │
┌─────────▼───────────┐
│ json_logger.py      │ ◄─── Our callback implementation
│ log_event_to_json() │
└─────────┬───────────┘
          │
┌─────────▼───────────┐
│   JSON Log Files    │
│ session_logs/*.jsonl│
└─────────────────────┘
```

This approach provides an alternative to the LoggedSessionService decorator pattern, allowing logging to be added through callback registration.
<!-- architecture-end -->

<!-- contents-start -->
## Contents

### Core Components

- **`json_logger.py`**: Callback-based JSON logging implementation
  - `log_event_to_json()`: Main callback function for event logging
  - `_serialize_event()`: Event serialization utility (similar to logging module)
  - Alternative implementation to the sessions-based logging approach

### Key Features

- **Callback-Based**: Uses Google ADK's native callback system
- **Non-Intrusive**: No need to modify session service implementations
- **Event Serialization**: Complete event data capture in JSON format
- **Session Isolation**: Separate log files per session
- **Error Resilience**: Graceful handling of logging failures
- **Alternative Architecture**: Different approach from LoggedSessionService
<!-- contents-end -->

## Usage Examples

### Basic Callback Registration

```python
from underlines_adk.callbacks.json_logger import log_event_to_json
from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService
from underlines_adk.agents.wide_search_agent import exa_agent

# Create session service
session_service = InMemorySessionService()

# Create runner with callback
runner = Runner(
    agent=exa_agent,
    app_name="callback_app",
    session_service=session_service,
    callbacks=[log_event_to_json]  # Register our callback
)

# Use runner normally - events will be logged automatically
session = await session_service.create_session(
    app_name="callback_app",
    user_id="user123"
)

# All events during this run will be logged via callback
async for event in runner.run_async(
    user_id="user123",
    session_id=session.id,
    new_message=user_content
):
    # Process events as normal
    pass
```

### Multiple Callbacks

```python
from underlines_adk.callbacks.json_logger import log_event_to_json

def custom_callback(event, session):
    """Custom callback for additional processing"""
    if event.is_final_response():
        print(f"Final response received in session {session.id}")

def error_callback(event, session):
    """Callback specifically for error handling"""
    if event.error_code or event.error_message:
        print(f"Error in session {session.id}: {event.error_message}")

# Register multiple callbacks
runner = Runner(
    agent=exa_agent,
    app_name="multi_callback_app",
    session_service=session_service,
    callbacks=[
        log_event_to_json,    # JSON logging
        custom_callback,      # Custom processing
        error_callback        # Error handling
    ]
)
```

### Conditional Logging

```python
def conditional_json_logger(event, session):
    """Only log certain types of events"""
    # Only log final responses and errors
    if event.is_final_response() or event.error_code:
        log_event_to_json(event, session)

# Use conditional callback
runner = Runner(
    agent=exa_agent,
    app_name="conditional_app",
    session_service=session_service,
    callbacks=[conditional_json_logger]
)
```

## Callback Development

### Creating Custom Callbacks

```python
from google.adk.events import Event
from google.adk.sessions import Session
import json
from datetime import datetime

def custom_event_processor(event: Event, session: Session) -> None:
    """
    Custom callback for event processing.
    
    Args:
        event: The ADK event being processed
        session: The session in which the event occurred
    """
    try:
        # Custom processing logic
        if event.is_final_response():
            # Process final responses
            process_final_response(event, session)
        
        elif event.content and any('function_call' in part for part in event.content.parts):
            # Process function calls
            process_function_call(event, session)
        
        elif event.error_code:
            # Handle errors
            handle_error(event, session)
            
    except Exception as e:
        # Callback errors should not affect main processing
        import logging
        logging.error(f"Callback error in session {session.id}: {e}")

def process_final_response(event: Event, session: Session):
    """Process final response events"""
    response_text = event.content.parts[0].text if event.content else ""
    
    # Example: Save to database
    save_response_to_db(session.id, response_text)
    
    # Example: Send notification
    send_completion_notification(session.user_id, session.id)

def process_function_call(event: Event, session: Session):
    """Process function call events"""
    for part in event.content.parts:
        if 'function_call' in part:
            function_name = part['function_call']['name']
            function_args = part['function_call']['args']
            
            # Example: Log function usage
            log_function_usage(session.id, function_name, function_args)

def handle_error(event: Event, session: Session):
    """Handle error events"""
    error_info = {
        'session_id': session.id,
        'event_id': event.id,
        'error_code': event.error_code,
        'error_message': event.error_message,
        'timestamp': datetime.utcnow().isoformat()
    }
    
    # Example: Send to error tracking service
    send_to_error_tracker(error_info)
```

### Callback Best Practices

**Error Handling**:
```python
def robust_callback(event: Event, session: Session) -> None:
    """Callback with comprehensive error handling"""
    try:
        # Main callback logic
        process_event(event, session)
        
    except Exception as e:
        # Log error but don't re-raise
        import logging
        logger = logging.getLogger(__name__)
        logger.error(
            f"Callback failed for event {event.id} in session {session.id}: {e}",
            exc_info=True
        )
        
        # Optionally, record callback failures
        record_callback_failure(event.id, session.id, str(e))
```

**Performance Considerations**:
```python
def efficient_callback(event: Event, session: Session) -> None:
    """Callback optimized for performance"""
    # Quick checks first
    if not should_process_event(event):
        return
    
    # Avoid expensive operations in callbacks
    # Consider queuing for background processing
    if is_expensive_operation_needed(event):
        queue_for_background_processing(event, session)
    else:
        process_event_quickly(event, session)

def should_process_event(event: Event) -> bool:
    """Quick check to determine if event needs processing"""
    return (
        event.is_final_response() or 
        event.error_code is not None or
        has_function_calls(event)
    )
```

## Comparison with LoggedSessionService

### Callback Approach Advantages

1. **No Service Wrapping**: Works with any session service without modification
2. **Multiple Processors**: Easy to register multiple callbacks for different purposes
3. **Selective Processing**: Can easily filter which events to process
4. **Native ADK Integration**: Uses Google ADK's built-in callback system

### LoggedSessionService Advantages

1. **Transparent**: All events logged automatically without explicit registration
2. **Simpler Setup**: Single service wrapper handles everything
3. **Guaranteed Logging**: Less chance of missing events due to callback registration issues
4. **Consistent Interface**: Maintains standard session service interface

### When to Use Each

**Use Callbacks When**:
- You need selective event processing
- You want multiple different processors
- You can't modify the session service creation
- You need fine-grained control over what gets logged

**Use LoggedSessionService When**:
- You want comprehensive logging of all events
- You prefer a simpler, more transparent approach
- You can control session service creation
- You want guaranteed logging without callback management

## Advanced Usage

### Async Callbacks

```python
import asyncio
from typing import Awaitable

def async_callback_wrapper(async_callback):
    """Wrapper to use async functions as callbacks"""
    def sync_callback(event: Event, session: Session) -> None:
        # Run async callback in event loop
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If loop is running, schedule as task
                loop.create_task(async_callback(event, session))
            else:
                # If no loop, run directly
                loop.run_until_complete(async_callback(event, session))
        except Exception as e:
            import logging
            logging.error(f"Async callback error: {e}")
    
    return sync_callback

async def async_event_processor(event: Event, session: Session):
    """Async callback for event processing"""
    if event.is_final_response():
        # Async operations like database writes, API calls
        await save_to_database(session.id, event.content)
        await send_webhook_notification(session.id)

# Use async callback
sync_processor = async_callback_wrapper(async_event_processor)
runner = Runner(
    agent=exa_agent,
    callbacks=[sync_processor]
)
```

### Callback Chaining

```python
class CallbackChain:
    """Chain multiple callbacks with error isolation"""
    
    def __init__(self, callbacks: list):
        self.callbacks = callbacks
    
    def __call__(self, event: Event, session: Session) -> None:
        """Execute all callbacks in chain"""
        for callback in self.callbacks:
            try:
                callback(event, session)
            except Exception as e:
                # Log error but continue with other callbacks
                import logging
                logging.error(f"Callback {callback.__name__} failed: {e}")

# Create callback chain
callback_chain = CallbackChain([
    log_event_to_json,
    custom_processor,
    error_handler,
    metrics_collector
])

runner = Runner(
    agent=exa_agent,
    callbacks=[callback_chain]
)
```

### Event Filtering

```python
def create_filtered_callback(filter_func, callback_func):
    """Create a callback that only processes filtered events"""
    def filtered_callback(event: Event, session: Session) -> None:
        if filter_func(event, session):
            callback_func(event, session)
    
    return filtered_callback

# Filter functions
def only_errors(event: Event, session: Session) -> bool:
    return event.error_code is not None

def only_final_responses(event: Event, session: Session) -> bool:
    return event.is_final_response()

def only_function_calls(event: Event, session: Session) -> bool:
    if not event.content:
        return False
    return any('function_call' in part for part in event.content.parts)

# Create filtered callbacks
error_logger = create_filtered_callback(only_errors, log_event_to_json)
response_processor = create_filtered_callback(only_final_responses, process_response)
function_tracker = create_filtered_callback(only_function_calls, track_function_usage)

runner = Runner(
    agent=exa_agent,
    callbacks=[error_logger, response_processor, function_tracker]
)
```

<!-- test-refs-start -->
## Testing

### Unit Tests
- Test callback functions with mock events and sessions
- Test error handling and edge cases
- Test event serialization consistency
- Verify callback registration and execution

### Integration Tests
- Test callbacks with real ADK runners
- Verify log file creation and content
- Test callback interaction with session services

### Running Tests
```bash
# Callback-specific tests
pytest tests/ -k callback

# Test callback registration
pytest tests/unit/ -k json_logger
```
<!-- test-refs-end -->

<!-- doc-refs-start -->
## Documentation

- **[Logging Guide](../../.docs/LOGGING.md)**: Overall logging system documentation
- **[Architecture Guide](../../.docs/ARCHITECTURE.md)**: How callbacks fit into the system
- **[Workflows Guide](../../.docs/WORKFLOWS.md)**: Development, usage, and maintenance workflows
- **[Sessions Documentation](../sessions/README.md)**: Alternative LoggedSessionService approach
- **Google ADK Documentation**: For callback system details
<!-- doc-refs-end -->

<!-- dependent-refs-start -->
## Dependencies

### External Dependencies
- **google-adk**: Event and Session types, callback system
- **Standard library**: json, logging, datetime, pathlib

### Internal Dependencies
- **Similar to logging module**: Shares event serialization patterns
- **No direct internal imports**: Can be used independently

### Dependents
- **Applications**: External applications can use callbacks for event processing
- **Tests**: Test suites may use callbacks for verification
- **Examples**: Example applications may demonstrate callback usage
<!-- dependent-refs-end -->

<!-- troubleshooting-start -->
## Troubleshooting

### Callbacks Not Executing

**Check Registration**:
```python
# Verify callback is registered
runner = Runner(
    agent=exa_agent,
    callbacks=[log_event_to_json]
)

print(f"Registered callbacks: {len(runner.callbacks)}")
for i, callback in enumerate(runner.callbacks):
    print(f"  {i}: {callback.__name__}")
```

**Check Callback Signature**:
```python
# Callbacks must have this exact signature
def correct_callback(event: Event, session: Session) -> None:
    pass

# This will NOT work
def incorrect_callback(event):  # Missing session parameter
    pass
```

### Callback Errors

**Debug Callback Execution**:
```python
def debug_callback(event: Event, session: Session) -> None:
    """Callback with debug information"""
    print(f"Processing event {event.id} in session {session.id}")
    
    try:
        # Your callback logic here
        actual_callback_logic(event, session)
        print(f"  Success: {event.id}")
        
    except Exception as e:
        print(f"  Error: {event.id} - {e}")
        import traceback
        traceback.print_exc()
```

### Performance Issues

**Slow Callbacks**:
- Callbacks block event processing
- Keep callback logic lightweight
- Use background queues for expensive operations

```python
import queue
import threading

# Background processing queue
processing_queue = queue.Queue()

def background_processor():
    """Background thread for expensive operations"""
    while True:
        try:
            event, session = processing_queue.get(timeout=1)
            expensive_operation(event, session)
            processing_queue.task_done()
        except queue.Empty:
            continue

# Start background thread
threading.Thread(target=background_processor, daemon=True).start()

def lightweight_callback(event: Event, session: Session) -> None:
    """Lightweight callback that queues expensive work"""
    # Quick processing
    if should_process_expensive(event):
        processing_queue.put((event, session))
    
    # Immediate processing for simple operations
    simple_operation(event, session)
```

### Log File Issues

**Same issues as logging module**:
- Check file permissions
- Verify disk space
- Validate JSON format
- Monitor file sizes

**Callback-Specific Issues**:
```python
def robust_json_callback(event: Event, session: Session) -> None:
    """JSON callback with additional error handling"""
    try:
        log_event_to_json(event, session)
    except Exception as e:
        # Fallback logging
        import logging
        logging.error(f"JSON callback failed for {event.id}: {e}")
        
        # Try alternative logging
        try:
            simple_log_fallback(event, session)
        except:
            pass  # Give up gracefully
```
<!-- troubleshooting-end -->
