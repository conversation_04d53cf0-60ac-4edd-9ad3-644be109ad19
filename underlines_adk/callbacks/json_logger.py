"""
Callback-based JSON logging for Google ADK events.

This module provides an alternative approach to event logging using Google ADK's
callback system. Instead of wrapping session services, it uses callbacks to
intercept and log events during agent execution.

Key Functions:
    - log_event_to_json: Main callback function for event logging
    - _serialize_event: Event serialization utility

Features:
    - Callback-based logging using Google ADK's native callback system
    - Complete event serialization to JSON Lines format
    - Session isolation with separate log files
    - Non-intrusive - no session service modification required
    - Error resilience with graceful failure handling

Callback Registration:
    from underlines_adk.callbacks.json_logger import log_event_to_json
    from google.adk.runners import Runner

    runner = Runner(
        agent=my_agent,
        callbacks=[log_event_to_json]  # Register callback
    )

Comparison with LoggedSessionService:
    - Callbacks: More flexible, can be selective, multiple processors
    - LoggedSessionService: Simpler setup, guaranteed comprehensive logging

File Format:
    - Location: {LOG_DIRECTORY}/{session_id}.jsonl
    - Format: One JSON object per line (JSON Lines)
    - Encoding: UTF-8
    - Content: Complete event data with metadata

Dependencies:
    - google.adk.events: Event objects for processing
    - google.adk.sessions: Session objects for context
    - Standard library: json, logging, datetime, pathlib
"""

import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Any

from google.adk.events import Event
from google.adk.sessions import Session

# Directory where session log files are stored
LOG_DIRECTORY = "session_logs"

# Logger for callback errors and debugging
logger = logging.getLogger(__name__)


def _serialize_event(event: Event) -> dict[str, Any]:
    """
    Convert an ADK Event object to a serializable dictionary.

    This function attempts to extract all relevant information from an event
    into a JSON-friendly format.

    Args:
        event: The ADK Event to serialize.

    Returns:
        A dictionary representing the event.
    """
    try:
        # Start with the basics from the event object
        event_data = {
            "timestamp_utc": datetime.utcnow().isoformat(),
            "event_id": event.id,
            "author": event.author,
            "invocation_id": event.invocation_id,
            "is_final_response": event.is_final_response(),
            "turn_complete": event.turn_complete,
            "content": None,
            "actions": None,
            "error_code": event.error_code,
            "error_message": event.error_message,
        }

        # Serialize content if present
        if event.content:
            parts_data = []
            for part in event.content.parts:
                part_data = {}
                if part.text:
                    part_data["text"] = part.text
                if fn_call := part.function_call:
                    part_data["function_call"] = {
                        "name": fn_call.name,
                        "args": fn_call.args,
                    }
                if fn_response := part.function_response:
                    part_data["function_response"] = {
                        "name": fn_response.name,
                        "response": fn_response.response,
                    }
                parts_data.append(part_data)
            event_data["content"] = {"role": event.content.role, "parts": parts_data}

        # Serialize actions if present
        if event.actions:
            event_data["actions"] = {
                "state_delta": event.actions.state_delta,
                "artifact_delta": event.actions.artifact_delta,
                "transfer_to_agent": event.actions.transfer_to_agent,
                "escalate": event.actions.escalate,
                "skip_summarization": event.actions.skip_summarization,
            }

        return event_data

    except Exception as e:
        logger.error(f"Error serializing event {event.id}: {e}")
        return {
            "timestamp_utc": datetime.utcnow().isoformat(),
            "error": "Failed to serialize event",
            "event_id": event.id,
            "exception": str(e),
        }


def log_event_to_json(event: Event, session: Session) -> None:
    """
    Google ADK callback function to log events to JSON Lines files.

    This function serves as a callback for Google ADK's Runner, automatically
    logging all events that occur during agent execution. It provides an
    alternative to the LoggedSessionService approach by using the callback
    system instead of wrapping session services.

    The callback creates one log file per session in JSON Lines format, with
    each event serialized as a complete JSON object on its own line. This
    format is ideal for streaming processing and analysis tools.

    Callback Signature:
        This function follows Google ADK's callback signature requirements:
        callback(event: Event, session: Session) -> None

    File Operations:
        - Creates LOG_DIRECTORY if it doesn't exist
        - Creates or appends to {session_id}.jsonl
        - Uses UTF-8 encoding for international character support
        - One JSON object per line (JSON Lines format)

    Args:
        event: The Google ADK Event object being processed. Contains all
            event data including metadata, content, actions, and errors.
        session: The Session object providing context for the event.
            Used to determine the log file name and add session metadata.

    Returns:
        None. This function performs side effects (file writing) only.

    Side Effects:
        - Creates LOG_DIRECTORY if it doesn't exist
        - Creates or appends to session log file
        - Logs errors to Python logging system if failures occur

    Error Handling:
        - Missing session ID: Warning logged, no file operation
        - Directory/file errors: Error logged, callback continues
        - Serialization errors: Handled gracefully with error logging
        - Callback errors do not propagate to prevent agent disruption

    Usage:
        # Register as callback with Runner
        from underlines_adk.callbacks.json_logger import log_event_to_json

        runner = Runner(
            agent=my_agent,
            callbacks=[log_event_to_json]
        )

        # Events will be automatically logged during execution
        async for event in runner.run_async(...):
            # Process events normally
            pass

    Note:
        This callback is designed to be non-intrusive and fault-tolerant.
        Logging failures will not interrupt agent execution. For guaranteed
        logging, consider using LoggedSessionService instead.
    """
    if not session or not session.id:
        logger.warning("Callback received an event with no session ID. Skipping log.")
        return

    try:
        log_dir = Path(LOG_DIRECTORY)
        log_dir.mkdir(exist_ok=True)

        log_file = log_dir / f"{session.id}.jsonl"
        event_data = _serialize_event(event)
        event_data["session_id"] = session.id  # Add session_id for context

        with open(log_file, "a", encoding="utf-8") as f:
            json.dump(event_data, f, ensure_ascii=False)
            f.write("\n")

    except Exception as e:
        logger.error(
            f"Failed to log event for session {session.id}: {e}", exc_info=True
        )
