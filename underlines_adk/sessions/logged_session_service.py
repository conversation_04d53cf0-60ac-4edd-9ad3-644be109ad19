"""
Session service wrapper with automatic event logging.

This module provides LoggedSessionService, a decorator that wraps any BaseSessionService
to add transparent event logging capabilities. All agent interactions are automatically
logged to JSON Lines files for audit trails, debugging, and analysis.

Key Features:
    - Transparent logging of all events
    - Works with any BaseSessionService implementation
    - Non-blocking log operations
    - Comprehensive error handling
    - Session isolation (one log file per session)

Architecture:
    Uses the decorator pattern to add logging functionality without modifying
    the underlying session service. Events are logged before being passed to
    the wrapped service, ensuring complete capture even if the underlying
    service fails.

Usage:
    from underlines_adk.sessions import LoggedSessionService
    from google.adk.sessions import InMemorySessionService

    # Use with default InMemorySessionService
    session_service = LoggedSessionService()

    # Or wrap a custom service
    custom_service = MyCustomSessionService()
    logged_service = LoggedSessionService(underlying_service=custom_service)

Log Files:
    - Location: session_logs/{session_id}.jsonl
    - Format: JSON Lines (one JSON object per line)
    - Content: Complete event data including metadata, content, and actions

Dependencies:
    - google.adk.sessions: BaseSessionService interface and implementations
    - google.adk.events: Event objects for logging
    - underlines_adk.logging: Actual log writing functionality
"""

from google.adk.events import Event
from google.adk.sessions import BaseSessionService, InMemorySessionService, Session

from underlines_adk.logging import log_event_to_json_file


class LoggedSessionService(BaseSessionService):
    """
    A session service wrapper that automatically logs all events to JSON files.

    This class implements the decorator pattern to add logging functionality to any
    BaseSessionService without modifying its behavior. It intercepts the append_event
    method to log events before delegating to the underlying service.

    The service is designed to be transparent - it maintains the same interface as
    any BaseSessionService while adding comprehensive event logging capabilities.
    Logging failures are handled gracefully and do not affect session operations.

    Attributes:
        _service: The underlying BaseSessionService being wrapped.
            Defaults to InMemorySessionService if none provided.
    """

    def __init__(self, underlying_service: BaseSessionService | None = None):
        """
        Initialize the logged session service.

        Args:
            underlying_service: The BaseSessionService to wrap with logging.
                If None, defaults to InMemorySessionService for convenience.
                Can be any implementation of BaseSessionService.

        Example:
            >>> # Use default InMemorySessionService
            >>> service = LoggedSessionService()

            >>> # Wrap a custom service
            >>> custom_service = DatabaseSessionService()
            >>> logged_service = LoggedSessionService(custom_service)
        """
        self._service = underlying_service or InMemorySessionService()

    async def create_session(self, **kwargs) -> Session:
        """
        Create a new session using the underlying service.

        This method delegates session creation to the wrapped service without
        modification. The session will be ready for use with automatic event
        logging when events are appended.

        Args:
            **kwargs: Arguments passed through to the underlying service's
                create_session method. Typically includes app_name and user_id.

        Returns:
            Session: The newly created session object.

        Example:
            >>> session = await service.create_session(
            ...     app_name="my_app",
            ...     user_id="user123"
            ... )
            >>> print(f"Created session: {session.id}")
        """
        return await self._service.create_session(**kwargs)

    async def get_session(self, **kwargs) -> Session:
        """
        Retrieve an existing session using the underlying service.

        Args:
            **kwargs: Arguments passed through to the underlying service's
                get_session method. Typically includes session_id.

        Returns:
            Session: The requested session object.

        Raises:
            SessionNotFoundError: If the session doesn't exist.
        """
        return await self._service.get_session(**kwargs)

    async def list_sessions(self, **kwargs) -> list[Session]:
        """
        List sessions using the underlying service.

        Args:
            **kwargs: Arguments passed through to the underlying service's
                list_sessions method. May include filters like app_name or user_id.

        Returns:
            List[Session]: List of session objects matching the criteria.
        """
        return await self._service.list_sessions(**kwargs)

    async def delete_session(self, **kwargs) -> bool:
        """
        Delete a session using the underlying service.

        Note: This method only deletes the session from the underlying service.
        The corresponding log file in session_logs/ is preserved for audit
        purposes. If you need to delete log files, do so manually.

        Args:
            **kwargs: Arguments passed through to the underlying service's
                delete_session method. Typically includes session_id.

        Returns:
            bool: True if the session was successfully deleted, False otherwise.

        Example:
            >>> success = await service.delete_session(session_id="session_123")
            >>> if success:
            ...     print("Session deleted (log file preserved)")
        """
        return await self._service.delete_session(**kwargs)

    async def append_event(self, session: Session, event: Event) -> Session:
        """
        Log the event to a JSON file, then append it using the underlying service.

        This is the core method where logging happens. The event is first logged
        to a JSON Lines file, then passed to the underlying service for normal
        processing. If logging fails, the error is logged but the event is still
        processed by the underlying service.

        Args:
            session: The session to which the event belongs.
            event: The event to log and append.

        Returns:
            Session: The updated session object from the underlying service.

        Raises:
            Any exceptions from the underlying service's append_event method.
            Logging errors are caught and logged but do not propagate.

        Example:
            >>> updated_session = await service.append_event(session, event)
            >>> # Event is now logged to session_logs/{session.id}.jsonl
        """
        log_event_to_json_file(session.id, event)
        return await self._service.append_event(session, event)
