# Sessions Module

<!-- purpose-start -->
This module provides session management services with automatic event logging capabilities. It extends Google ADK's session framework to add transparent logging of all agent interactions, enabling comprehensive audit trails and debugging capabilities.
<!-- purpose-end -->

<!-- architecture-start -->
## Architecture

The module uses a decorator pattern to add logging functionality to any session service:

```
┌─────────────────────┐
│   Application       │
│                     │
└─────────┬───────────┘
          │
┌─────────▼───────────┐
│ LoggedSessionService│ ◄─── Decorator Pattern
│                     │
└─────────┬───────────┘
          │
┌─────────▼───────────┐
│ BaseSessionService  │ ◄─── Any underlying service
│ (InMemory/Custom)   │
└─────────────────────┘
          │
┌─────────▼───────────┐
│   JSON Log Files    │
│ session_logs/*.jsonl│
└─────────────────────┘
```

This design allows logging to be added to any session service without changing the underlying implementation.
<!-- architecture-end -->

<!-- contents-start -->
## Contents

### Core Components

- **`logged_session_service.py`**: Main session service with logging
  - `LoggedSessionService`: Decorator class that adds logging to any BaseSessionService
  - Transparent logging of all events
  - Configurable underlying session service
  - Non-blocking log operations

### Key Features

- **Automatic Logging**: All events logged without manual intervention
- **Flexible Backend**: Works with any BaseSessionService implementation
- **Error Resilience**: Logging failures don't affect session operations
- **JSON Lines Format**: Easy to parse and analyze log files
- **Session Isolation**: Each session gets its own log file
<!-- contents-end -->

## Usage Examples

### Basic Usage

```python
from underlines_adk.sessions import LoggedSessionService
from google.adk.runners import Runner
from underlines_adk.agents.wide_search_agent import exa_agent

# Create logged session service (uses InMemorySessionService by default)
session_service = LoggedSessionService()

# Use with runner
runner = Runner(
    agent=exa_agent,
    app_name="my_search_app",
    session_service=session_service
)

# Create session - will be logged automatically
session = await session_service.create_session(
    app_name="my_search_app",
    user_id="user123"
)

print(f"Session created: {session.id}")
print(f"Logs will be saved to: session_logs/{session.id}.jsonl")
```

### Custom Underlying Service

```python
from google.adk.sessions import BaseSessionService
from underlines_adk.sessions import LoggedSessionService

class DatabaseSessionService(BaseSessionService):
    """Custom session service that stores sessions in database"""
    
    async def create_session(self, **kwargs):
        # Custom database logic
        pass
    
    async def append_event(self, session, event):
        # Custom database storage
        pass
    
    # ... other methods

# Wrap custom service with logging
db_service = DatabaseSessionService()
logged_db_service = LoggedSessionService(underlying_service=db_service)

# Now all database operations are also logged to files
runner = Runner(
    agent=exa_agent,
    app_name="db_app",
    session_service=logged_db_service
)
```

### Session Management

```python
# Create multiple sessions
session1 = await session_service.create_session(
    app_name="app1", user_id="user1"
)
session2 = await session_service.create_session(
    app_name="app1", user_id="user2"
)

# Each gets its own log file:
# session_logs/{session1.id}.jsonl
# session_logs/{session2.id}.jsonl

# List all sessions
sessions = await session_service.list_sessions(app_name="app1")

# Get specific session
session = await session_service.get_session(session_id=session1.id)

# Delete session (note: log file is preserved)
await session_service.delete_session(session_id=session1.id)
```

## Log File Analysis

### Reading Log Files

```python
import json
from pathlib import Path

def read_session_logs(session_id: str):
    """Read and parse logs for a specific session"""
    log_file = Path(f"session_logs/{session_id}.jsonl")
    
    if not log_file.exists():
        print(f"No log file found for session {session_id}")
        return []
    
    events = []
    with open(log_file, 'r') as f:
        for line in f:
            events.append(json.loads(line))
    
    return events

# Usage
events = read_session_logs("session_abc123")
print(f"Found {len(events)} events")

for event in events:
    print(f"Event {event['event_id']} by {event['author']}")
    if event.get('is_final_response'):
        print("  -> Final response")
```

### Log Analysis Examples

```python
def analyze_session_performance(session_id: str):
    """Analyze performance metrics for a session"""
    events = read_session_logs(session_id)
    
    if not events:
        return None
    
    # Calculate session duration
    start_time = events[0]['timestamp_utc']
    end_time = events[-1]['timestamp_utc']
    
    # Count different event types
    user_messages = sum(1 for e in events if e['author'] == 'user')
    agent_responses = sum(1 for e in events if e['author'] != 'user')
    final_responses = sum(1 for e in events if e.get('is_final_response'))
    
    # Count function calls
    function_calls = 0
    for event in events:
        content = event.get('content', {})
        parts = content.get('parts', [])
        function_calls += sum(1 for part in parts if 'function_call' in part)
    
    return {
        'session_id': session_id,
        'total_events': len(events),
        'user_messages': user_messages,
        'agent_responses': agent_responses,
        'final_responses': final_responses,
        'function_calls': function_calls,
        'start_time': start_time,
        'end_time': end_time
    }

# Usage
metrics = analyze_session_performance("session_abc123")
if metrics:
    print(f"Session {metrics['session_id']}:")
    print(f"  Total events: {metrics['total_events']}")
    print(f"  Function calls: {metrics['function_calls']}")
    print(f"  Final responses: {metrics['final_responses']}")
```

### Error Detection

```python
def find_session_errors(session_id: str):
    """Find errors in session logs"""
    events = read_session_logs(session_id)
    
    errors = []
    for event in events:
        if event.get('error_code') or event.get('error_message'):
            errors.append({
                'event_id': event['event_id'],
                'timestamp': event['timestamp_utc'],
                'error_code': event.get('error_code'),
                'error_message': event.get('error_message'),
                'author': event['author']
            })
    
    return errors

# Usage
errors = find_session_errors("session_abc123")
if errors:
    print(f"Found {len(errors)} errors:")
    for error in errors:
        print(f"  {error['timestamp']}: {error['error_message']}")
```

## Configuration

### Log Directory

Default log directory is `session_logs/` in the current working directory.

To change the log directory:

```python
# Modify the LOG_DIRECTORY constant before creating sessions
from underlines_adk.logging import json_file_logger
json_file_logger.LOG_DIRECTORY = "/custom/log/path"

# Then create session service
session_service = LoggedSessionService()
```

### Error Handling

The logging system is designed to be non-intrusive:

- Log write failures are caught and logged to Python's logging system
- Session operations continue even if logging fails
- Missing session IDs are handled gracefully
- Disk space issues don't stop session operations

## Advanced Usage

### Custom Log Processing

```python
from underlines_adk.sessions import LoggedSessionService
from underlines_adk.logging import log_event_to_json_file

class CustomLoggedSessionService(LoggedSessionService):
    """Session service with custom log processing"""
    
    async def append_event(self, session, event):
        # Custom pre-processing
        if self.should_log_event(event):
            # Use standard logging
            log_event_to_json_file(session.id, event)
            
            # Add custom processing
            await self.process_event_custom(session, event)
        
        # Continue with standard flow
        return await self._service.append_event(session, event)
    
    def should_log_event(self, event):
        """Custom logic to determine if event should be logged"""
        # Example: only log final responses
        return event.is_final_response()
    
    async def process_event_custom(self, session, event):
        """Custom event processing"""
        # Example: send to external monitoring system
        pass
```

### Batch Log Analysis

```python
from pathlib import Path
import json

def analyze_all_sessions():
    """Analyze all session logs in the log directory"""
    log_dir = Path("session_logs")
    
    if not log_dir.exists():
        print("No log directory found")
        return
    
    session_stats = {}
    
    for log_file in log_dir.glob("*.jsonl"):
        session_id = log_file.stem
        
        event_count = 0
        error_count = 0
        
        with open(log_file, 'r') as f:
            for line in f:
                event = json.loads(line)
                event_count += 1
                
                if event.get('error_code') or event.get('error_message'):
                    error_count += 1
        
        session_stats[session_id] = {
            'events': event_count,
            'errors': error_count,
            'error_rate': error_count / event_count if event_count > 0 else 0
        }
    
    return session_stats

# Usage
stats = analyze_all_sessions()
for session_id, data in stats.items():
    print(f"Session {session_id}: {data['events']} events, {data['errors']} errors")
```

<!-- test-refs-start -->
## Testing

### Unit Tests
Located in `../../tests/unit/`:
- `test_logged_session_service.py`: Tests for LoggedSessionService behavior
- Mock underlying session services
- Test logging functionality
- Verify error handling

### Integration Tests
- Test with real session services
- Verify log file creation and content
- Test concurrent session handling

### Running Tests
```bash
# Session-specific tests
pytest tests/unit/test_logged_session_service.py

# All session tests
pytest tests/ -k session

# With temporary log directory
pytest tests/unit/test_logged_session_service.py -v
```
<!-- test-refs-end -->

<!-- doc-refs-start -->
## Documentation

- **[Logging Guide](../../.docs/LOGGING.md)**: Comprehensive logging documentation
- **[Architecture Guide](../../.docs/ARCHITECTURE.md)**: How sessions fit into the system
- **[Workflows Guide](../../.docs/WORKFLOWS.md)**: Development, usage, and maintenance workflows
- **[Main README](../../README.md)**: Project overview and setup
- **Google ADK Documentation**: For BaseSessionService interface details
<!-- doc-refs-end -->

<!-- dependent-refs-start -->
## Dependencies

### External Dependencies
- **google-adk**: BaseSessionService interface and session management
- **Standard library**: json, logging, pathlib

### Internal Dependencies
- **`../logging/json_file_logger`**: Actual log writing functionality
- **Minimal coupling**: Designed to work with any BaseSessionService

### Dependents
- **Examples**: Example scripts use LoggedSessionService
- **Applications**: External applications use for session management
- **Tests**: Integration tests depend on session services
<!-- dependent-refs-end -->

<!-- troubleshooting-start -->
## Troubleshooting

### Log Files Not Created
1. **Check Permissions**: Ensure write permissions for `session_logs/` directory
2. **Check Disk Space**: Verify sufficient disk space available
3. **Check Path**: Ensure log directory path is valid and accessible

```python
# Debug log file creation
import os
from pathlib import Path

log_dir = Path("session_logs")
print(f"Log directory exists: {log_dir.exists()}")
print(f"Log directory writable: {os.access(log_dir, os.W_OK)}")
```

### Session Operations Failing
1. **Check Underlying Service**: Test the wrapped service independently
2. **Review Error Logs**: Check Python logging output for errors
3. **Verify Session IDs**: Ensure session IDs are valid and unique

```python
# Test underlying service
from google.adk.sessions import InMemorySessionService

service = InMemorySessionService()
session = await service.create_session(app_name="test", user_id="test")
print(f"Underlying service works: {session.id}")
```

### Performance Issues
1. **Log File Size**: Monitor log file growth for large sessions
2. **Concurrent Sessions**: Test with multiple simultaneous sessions
3. **Disk I/O**: Consider SSD storage for high-volume logging

### Memory Issues
1. **Session Cleanup**: Ensure sessions are properly deleted when no longer needed
2. **Log Rotation**: Implement log rotation for long-running applications
3. **Event Size**: Monitor size of individual events being logged

### Development Issues

**Custom Session Service Integration**:
```python
# Verify custom service implements required interface
from google.adk.sessions import BaseSessionService

class MyService(BaseSessionService):
    # Must implement all abstract methods
    async def create_session(self, **kwargs): pass
    async def get_session(self, **kwargs): pass
    async def list_sessions(self, **kwargs): pass
    async def delete_session(self, **kwargs): pass
    async def append_event(self, session, event): pass
```

**Log Format Issues**:
- Ensure JSON serialization works for all event types
- Check for circular references in event data
- Verify timestamp formats are consistent
<!-- troubleshooting-end -->
