"""
Session management services for the Underlines ADK.

This module provides session management capabilities with automatic event logging.
It extends Google ADK's session framework to add transparent logging of all agent
interactions, enabling comprehensive audit trails and debugging capabilities.

Key Components:
    - LoggedSessionService: Session service wrapper with automatic logging

Features:
    - Transparent event logging to JSON Lines files
    - Works with any BaseSessionService implementation
    - Non-blocking log operations
    - Session isolation (one log file per session)
    - Comprehensive error handling

Usage:
    from underlines_adk.sessions import LoggedSessionService

    # Create session service with logging
    session_service = LoggedSessionService()

    # Use with Google ADK Runner
    runner = Runner(
        agent=my_agent,
        session_service=session_service
    )

Architecture:
    Uses the decorator pattern to add logging functionality to any session service
    without modifying its behavior. Events are logged before being passed to the
    underlying service, ensuring complete capture.

Dependencies:
    - google.adk.sessions: BaseSessionService interface
    - underlines_adk.logging: Event logging utilities
"""

from .logged_session_service import LoggedSessionService

__all__ = ["LoggedSessionService"]
