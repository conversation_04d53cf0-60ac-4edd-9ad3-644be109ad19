"""
Research Planner Agent for orchestrating newsletter research workflows.

This module provides the ResearchPlanner, an AI agent that coordinates existing
WideSearchAgent and DeepSearchAgent to create flexible newsletter generation pipelines.
The agent analyzes natural language newsletter requirements and plans research
approaches that leverage existing agent capabilities.

Key Features:
    - Natural language requirement analysis (no structured data extraction)
    - Dynamic research planning based on user specifications
    - Orchestration of existing WideSearchAgent and DeepSearchAgent
    - Universal flexibility for any newsletter type
    - Initial topic discovery using exa_wide_search

Agent Configuration:
    - Name: "ResearchPlanner"
    - Model: configurable via environment variables (default: Gemini 2.5 Flash Preview)
    - Tools: exa_wide_search
    - Output: Research plan for coordinating existing agents

Usage:
    from underlines_adk.agents.research_planner import research_planner
    from google.adk.runners import Runner

    runner = Runner(agent=research_planner, ...)

    # Input format: Natural language newsletter requirements
    # Example: "I need a weekly technology newsletter focusing on AI developments 
    #          and their business implications. Include 3-4 major stories with 
    #          analysis of market impact and competitive landscape."

Expected Output Format:
    Research plan describing:
    - Research approach tailored to user requirements
    - Topic areas identified for investigation
    - Strategy for leveraging existing WideSearchAgent and DeepSearchAgent
    - Quality expectations based on user specifications

Dependencies:
    - google.adk.agents.llm_agent: Core agent framework
    - ..tools.exa_tools: Exa API integration (exa_wide_search)
    - ..tools.litellm_tools: Language model configuration
"""

from google.adk.agents.llm_agent import LlmAgent

from ..tools.exa_tools import exa_wide_search
from ..tools.litellm_tools import llm

research_planner: LlmAgent = LlmAgent(
    name="ResearchPlanner",
    model=llm,
    description="Orchestrates research workflows by analyzing user requirements and planning coordination of existing WideSearchAgent and DeepSearchAgent.",
    instruction="""
    You are a research planning orchestrator that coordinates existing WideSearchAgent and DeepSearchAgent 
    to fulfill natural language newsletter requirements. Your role is to analyze user requirements and 
    plan research approaches that leverage existing agent capabilities.

    CRITICAL: Work directly with the user's natural language requirements. Do NOT extract structured data 
    or create rigid schemas. The existing agents are already perfectly flexible for any newsletter type.

    Your responsibilities:

    1. **Analyze User Requirements**: 
       - Read the user's natural language newsletter requirements carefully
       - Understand their specific needs, audience, format preferences, and quality expectations
       - Identify the scope, depth, and style they want

    2. **Initial Topic Discovery**:
       - Use exa_wide_search to discover relevant topics and get initial coverage
       - Explore the landscape of information available for their requirements
       - Identify key areas that need research attention

    3. **Plan Research Coordination**:
       - Design a research approach that leverages existing WideSearchAgent and DeepSearchAgent
       - Determine how to coordinate these agents based on user requirements
       - Plan the workflow that will best serve their specific newsletter type

    4. **Quality Planning**:
       - Establish quality expectations based on the user's stated requirements
       - Plan how to assess whether research meets their specific standards
       - Consider their target audience and use case

    OUTPUT: Provide a research plan that describes:
    - Your understanding of the user's requirements
    - Research approach tailored to their needs
    - How you'll coordinate existing WideSearchAgent and DeepSearchAgent
    - Quality standards based on their expectationss
    - Strategy for ensuring their specific newsletter type is well-served

    Remember: The existing agents are already perfect for any topic. Your job is orchestration planning,
    not rebuilding research capabilities. Trust the existing agent quality and plan how to coordinate them.
    """,
    tools=[exa_wide_search],
    output_key="research_plan",
)
