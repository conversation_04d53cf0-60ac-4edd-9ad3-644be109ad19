"""
Wide Search Agent for comprehensive web information gathering.

This module provides the WideSearchAgent, an AI agent that performs broad web searches
to gather comprehensive information about specific topics. The agent uses the Exa API
to search across the web and structures findings into organized stories with metadata.

Key Features:
    - Broad web search coverage (30 results with highlights)
    - Automatic deduplication of stories
    - Structured output with titles, URLs, summaries, and reasoning
    - Multiple search iterations for comprehensive coverage
    - Date estimation for temporal context

Agent Configuration:
    - Name: "WideSearchAgent"
    - Model: OpenAI GPT-4o-mini (via LiteLLM)
    - Tools: exa_wide_search
    - Output: JSON array of story objects

Usage:
    from underlines_adk.agents.wide_search_agent import exa_agent
    from google.adk.runners import Runner

    runner = Runner(agent=exa_agent, ...)

    # Query format: {"query": "search terms", "lookback_days": 7}

Expected Output Format:
    [
        {
            "title": "Story title",
            "reference_urls": ["url1", "url2"],
            "summary": "Brief summary",
            "reasoning": "Why this is relevant",
            "date": "2024-01-15"
        }
    ]

Dependencies:
    - google.adk.agents.llm_agent: Core agent framework
    - ..tools.exa_tools: Exa API integration
    - ..tools.litellm_tools: Language model configuration
"""

from google.adk.agents.llm_agent import LlmAgent

from ..tools.exa_tools import exa_wide_search
from ..tools.litellm_tools import llm

exa_agent: LlmAgent = LlmAgent(
    name="WideSearchAgent",
    model=llm,
    description="Gathers timely information about specific topics from across the web, adapting to user requirements.",
    instruction="""
       CONTEXT-AWARE RESEARCH: Read the user's requirements from session state to understand their specific newsletter needs.

       Check session state for:
       - user_requirements: The user's specific newsletter requirements and target audience
       - research_plan: The research plan that guides your search strategy
       - current_focus: The specific focus area for this search iteration

       ADAPTIVE SEARCH STRATEGY: Use the exa_wide_search tool to fetch information that matches the user's specific requirements:
       - Adapt search queries to their newsletter type (biotech VC, fintech, daily news, etc.)
       - Focus on sources and content appropriate for their target audience
       - Consider their stated depth and coverage preferences
       - Match their quality expectations and source credibility requirements

       OUTPUT FORMAT: Group what you find into a list of stories, structured as follows:
       {
        "title": "Title of the story",
        "reference_urls": ["url1", "url2", "url3"],  // List of URLs referencing the story
        "summary": "Summary of the story",
        "reasoning": "Reasoning for why you think this story is relevant to the user's specific requirements",
        "date": "Best estimate for the date of the story",
       }

       SEARCH STRATEGY: Run multiple searches with different queries to ensure comprehensive coverage that matches the user's stated requirements.
       Stop searching when you feel no new stories are relevant to their specific newsletter type and audience.
       Make sure you do *not* include duplicate stories in your list.
       """,
    tools=[exa_wide_search],
    output_key="wide_search_results",
)
