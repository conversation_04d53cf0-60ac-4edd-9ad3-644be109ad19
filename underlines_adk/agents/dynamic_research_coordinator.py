"""
ADK-Native Dynamic Research Coordinator for orchestrating existing agents.

This module provides the DynamicResearchCoordinator, a simplified BaseAgent that uses
ADK's built-in orchestration capabilities to coordinate existing WideSearchAgent and
DeepSearchAgent instances. It follows ADK best practices by leveraging the framework's
native session state management and agent coordination patterns.

Key Features:
    - ADK-native direct agent coordination (no custom wrappers)
    - Leverages ADK's built-in session state management
    - Universal flexibility for any newsletter type
    - Direct ParallelAgent and LoopAgent orchestration
    - Trusts existing agents to read session state natively

ADK-Native Principles:
    - Uses ADK's ParallelAgent for direct coordination
    - Trusts framework's session state propagation
    - No manual context injection or custom wrappers
    - Leverages existing agent capabilities without duplication
    - Follows "trust the framework" philosophy

Usage:
    from underlines_adk.agents.dynamic_research_coordinator import DynamicResearchCoordinator
    from google.adk.runners import Runner

    coordinator = DynamicResearchCoordinator(
        name="ADKNativeResearchCoordinator",
        description="Coordinates research using ADK-native patterns"
    )

    runner = Runner(agent=coordinator, ...)

Dependencies:
    - google.adk.agents: BaseAgent, ParallelAgent, LoopAgent
    - google.adk.events: Event
    - .wide_search_agent: WideSearchAgent instance
    - .deep_search_agent: DeepSearchAgent instance
    - .quality_assessment_agent: QualityAssessmentAgent
"""

import os
from typing import AsyncGenerator
from google.adk.agents import BaseAgent, ParallelAgent, LoopAgent
from google.adk.events import Event, EventActions

from .wide_search_agent import exa_agent as wide_search_agent
from .deep_search_agent import exa_agent as deep_search_agent
from .quality_assessment_agent import QualityAssessmentAgent
from ..tools.litellm_tools import llm


class DynamicResearchCoordinator(BaseAgent):
    """
    ADK-native BaseAgent that orchestrates existing agents using framework capabilities.

    This coordinator uses ADK's built-in ParallelAgent and LoopAgent patterns to
    coordinate existing WideSearchAgent and DeepSearchAgent instances. It trusts
    ADK's session state management and avoids custom wrapper complexity.

    The coordinator leverages ADK's native orchestration capabilities, allowing
    existing agents to read session state directly through their instructions,
    maintaining universal flexibility for any newsletter type.
    """

    def __init__(self, name: str = "DynamicResearchCoordinator", description: str | None = None):
        """Initialize the DynamicResearchCoordinator."""
        if description is None:
            description = "Uses LLM intelligence to coordinate research using existing agents"

        super().__init__(
            name=name,
            description=description,
            sub_agents=[],  # We'll create sub-agents dynamically using LLM intelligence
            before_agent_callback=None,
            after_agent_callback=None
        )

    async def _run_async_impl(self, ctx) -> AsyncGenerator[Event, None]:
        """
        Story-centric workflow: Phase 1 (Story Discovery) and Phase 2 (Story Validation).

        Simple, elegant implementation following PRD.md architecture.
        """
        # Validate required inputs
        user_requirements = ctx.session.state.get("user_requirements", "")
        research_plan = ctx.session.state.get("research_plan", "")

        if not user_requirements:
            raise ValueError("No user requirements found for research coordination")
        if not research_plan:
            raise ValueError("No research plan found for research coordination")

        # Phase 1: Story Discovery
        async for event in self._story_discovery_phase(ctx, user_requirements, research_plan):
            yield event

        # Phase 2: Story Validation & Regrouping
        async for event in self._story_validation_phase(ctx, user_requirements):
            yield event

    async def _story_discovery_phase(self, ctx, user_requirements: str, research_plan: str) -> AsyncGenerator[Event, None]:
        """Phase 1: Discover stories through topic analysis and parallel search."""
        # Identify topic areas using LLM intelligence
        topic_areas = await self._identify_topic_areas(user_requirements, research_plan)

        # Save topic areas to session state
        yield Event(author=self.name, actions=EventActions(state_delta={"topic_areas": topic_areas}))

        # Run parallel WideSearchAgents for each topic
        search_agents = [wide_search_agent for _ in topic_areas]  # One agent per topic
        parallel_search = ParallelAgent(name="TopicSearch", sub_agents=search_agents)

        async for event in parallel_search.run_async(ctx):
            yield event

        # Aggregate discovered stories
        discovered_stories = await self._aggregate_stories(ctx)
        yield Event(author=self.name, actions=EventActions(state_delta={"discovered_stories": discovered_stories}))

    async def _story_validation_phase(self, ctx, user_requirements: str) -> AsyncGenerator[Event, None]:
        """Phase 2: Validate stories against requirements."""
        discovered_stories = ctx.session.state.get("discovered_stories", [])

        # Simple validation - trust LLM intelligence
        final_story_list = discovered_stories  # For Day 1, keep it simple
        yield Event(author=self.name, actions=EventActions(state_delta={"final_story_list": final_story_list}))

    async def _identify_topic_areas(self, user_requirements: str, research_plan: str) -> list[str]:
        """Use LLM intelligence to identify topic areas from requirements."""
        # Use LLM to analyze user requirements and research plan
        topic_analysis_prompt = f"""
        Analyze these newsletter requirements and research plan to identify the key topic areas for story discovery.

        USER REQUIREMENTS:
        {user_requirements}

        RESEARCH PLAN:
        {research_plan}

        TASK:
        Based on the user's specific newsletter requirements, identify 3-5 distinct topic areas that should be researched.
        These topic areas should be:
        - Specific to the user's newsletter type and audience
        - Broad enough to capture relevant stories
        - Distinct from each other to avoid overlap

        RESPONSE FORMAT:
        Return only a simple list of topic areas, one per line, without numbering or bullets.

        Example for a biotech VC newsletter focused on TL1A:
        TL1A research developments
        TL1A Clinical trial updates
        TL1A Biotech company funding
        TL1A Regulatory changes
        TL1A Market analysis

        Example for a daily tech newsletter:
        AI and machine learning trends
        Software development trends
        Tech industry news
        Tech startup funding
        Tech product launches
        """

        try:
            llm_response = await llm.agenerate(
                messages=[{"role": "user", "content": topic_analysis_prompt}],
                max_tokens=int(os.getenv("MAX_TOPIC_ANALYSIS_TOKENS", "500"))
            )

            # Extract topic areas from LLM response
            llm_output = llm_response.generations[0][0].text.strip()

            # Parse topic areas from LLM response (simple line-by-line parsing)
            topic_areas = [line.strip() for line in llm_output.split('\n') if line.strip()]

            # Ensure we have at least one topic area
            if not topic_areas:
                topic_areas = ["general research"]  # Minimal fallback

            return topic_areas

        except Exception as e:
            # If LLM call fails, raise clear error
            raise ValueError(f"Failed to identify topic areas: {str(e)}")

    async def _aggregate_stories(self, ctx) -> list[dict]:
        """Aggregate stories from parallel search results."""
        # Simple aggregation for Day 1 - collect from session state
        # In a real implementation, this would aggregate and deduplicate stories
        return []  # Placeholder for Day 1

    def create_quality_enhanced_research_loop(self, area_name: str = "research") -> LoopAgent:
        """
        Create ADK-native LoopAgent that coordinates existing agents with quality assessment.

        This method creates a LoopAgent that directly coordinates the existing
        WideSearchAgent and DeepSearchAgent with agentic quality assessment.
        Uses ADK's built-in session state management - no manual context injection needed.

        Args:
            area_name: Optional identifier for this research area (default: "research")

        Returns:
            LoopAgent configured with existing agents and agentic quality assessment
        """
        # Create agentic quality assessment agent
        quality_agent = QualityAssessmentAgent(
            name=f"QualityAssessor_{area_name}",
            description=f"Evaluates research quality using LLM intelligence"
        )

        # ADK-native direct agent coordination (agents read session state automatically)
        return LoopAgent(
            name=f"QualityEnhancedResearch_{area_name}",
            sub_agents=[
                wide_search_agent,  # Reads session state natively
                deep_search_agent,  # Reads session state natively
                quality_agent       # Agentic quality assessment with escalation
            ],
            max_iterations=int(os.getenv("MAX_RESEARCH_ITERATIONS", "3"))
        )
