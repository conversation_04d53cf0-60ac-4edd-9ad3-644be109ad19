"""
ADK-Native Dynamic Research Coordinator for orchestrating existing agents.

This module provides the DynamicResearchCoordinator, a modular BaseAgent that uses
ADK's built-in orchestration capabilities to coordinate existing WideSearchAgent and
DeepSearchAgent instances through separate phase implementations.

Key Features:
    - Modular phase-based architecture for maintainability
    - ADK-native direct agent coordination (no custom wrappers)
    - Leverages ADK's built-in session state management
    - Universal flexibility for any newsletter type
    - Story-centric workflow following PRD.md architecture

Architecture:
    - StoryDiscoveryPhase: Phase 1 - Topic analysis and parallel story discovery
    - StoryValidationPhase: Phase 2 - Story validation and regrouping
    - DeepResearchPhase: Phase 3 - Per-story deep research with quality assessment

ADK-Native Principles:
    - Uses ADK's ParallelAgent and LoopAgent for coordination
    - Trusts framework's session state propagation
    - No manual context injection or custom wrappers
    - Leverages existing agent capabilities without duplication
    - Follows "trust the framework" philosophy

Usage:
    from underlines_adk.agents.dynamic_research_coordinator import DynamicResearchCoordinator
    from google.adk.runners import Runner

    coordinator = DynamicResearchCoordinator(
        name="ModularResearchCoordinator",
        description="Coordinates research using modular phase-based architecture"
    )

    runner = Runner(agent=coordinator, ...)

Dependencies:
    - google.adk.agents: BaseAgent
    - google.adk.events: Event
    - .phases: StoryDiscoveryPhase, StoryValidationPhase, DeepResearchPhase
"""

from typing import AsyncGenerator
from google.adk.agents import BaseAgent
from google.adk.events import Event

from .phases.story_discovery_phase import StoryDiscoveryPhase
from .phases.story_validation_phase import StoryValidationPhase
from .phases.deep_research_phase import DeepResearchPhase


class DynamicResearchCoordinator(BaseAgent):
    """
    Modular BaseAgent that orchestrates story-centric workflow through separate phases.

    This coordinator implements the story-centric workflow architecture defined in PRD.md
    using separate phase classes for improved maintainability and testability. Each phase
    is implemented as a separate class following ADK-native patterns.

    Architecture:
    - Phase 1: StoryDiscoveryPhase - Topic analysis and parallel story discovery
    - Phase 2: StoryValidationPhase - Story validation and regrouping
    - Phase 3: DeepResearchPhase - Per-story deep research with quality assessment

    The coordinator maintains universal flexibility for any newsletter type while
    providing clear separation of concerns through modular phase implementations.
    """

    def __init__(self, name: str = "DynamicResearchCoordinator", description: str | None = None):
        """Initialize the DynamicResearchCoordinator with phase instances."""
        if description is None:
            description = "Orchestrates story-centric workflow through modular phase implementations"

        super().__init__(
            name=name,
            description=description,
            sub_agents=[],  # Phases handle their own agent coordination
            before_agent_callback=None,
            after_agent_callback=None
        )

        # Initialize phase instances
        self.story_discovery_phase = StoryDiscoveryPhase(coordinator_name=name)
        self.story_validation_phase = StoryValidationPhase(coordinator_name=name)
        self.deep_research_phase = DeepResearchPhase(coordinator_name=name)

    async def _run_async_impl(self, ctx) -> AsyncGenerator[Event, None]:
        """
        Execute story-centric workflow through modular phase implementations.

        Implements the complete story-centric workflow defined in PRD.md:
        - Phase 1: Story Discovery (topic analysis + parallel WideSearchAgents)
        - Phase 2: Story Validation & Regrouping (with iteration capability)
        - Phase 3: Deep Research Per Story (parallel LoopAgents with quality assessment)
        """
        # Validate required inputs (explicit error handling - MANDATORY)
        user_requirements = ctx.session.state.get("user_requirements", "")
        research_plan = ctx.session.state.get("research_plan", "")

        if not user_requirements:
            raise ValueError("No user requirements found for research coordination")
        if not research_plan:
            raise ValueError("No research plan found for research coordination")

        # Phase 1: Story Discovery
        async for event in self.story_discovery_phase.execute(ctx, user_requirements, research_plan):
            yield event

        # Phase 2: Story Validation & Regrouping
        async for event in self.story_validation_phase.execute(ctx, user_requirements):
            yield event

        # Phase 3: Deep Research Per Story
        async for event in self.deep_research_phase.execute(ctx, user_requirements):
            yield event




