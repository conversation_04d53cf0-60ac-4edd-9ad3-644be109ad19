"""
Agentic Quality Assessment Agent for newsletter generation system.

This module provides the QualityAssessmentAgent, a custom BaseAgent that evaluates
research quality using LLM intelligence and natural language assessment. It follows
agentic principles by trusting LLM judgment completely without hardcoded return
structures or predetermined quality criteria.

Key Features:
    - LLM-driven quality evaluation in natural language
    - ADK-native Event escalation for loop termination
    - Universal flexibility across all newsletter types
    - Explicit error handling with clear ValueError messages
    - No hardcoded dictionary return structures

Usage:
    from underlines_adk.agents.quality_assessment_agent import QualityAssessmentAgent
    
    quality_agent = QualityAssessmentAgent(
        name="AgenticQualityAssessor",
        description="Evaluates research quality using LLM intelligence"
    )
    
    # Use in LoopAgent for iterative quality improvement
    loop_agent = LoopAgent(
        sub_agents=[research_agent, quality_agent],
        max_iterations=3
    )

Agentic Principles:
    - Trust LLM intelligence over programmatic validation
    - Use natural language assessment without fixed schemas
    - Escalate based on LLM judgment, not hardcoded criteria
    - Raise clear errors for missing inputs instead of silent handling

Dependencies:
    - google.adk.agents.BaseAgent
    - google.adk.events.Event, EventActions
    - underlines_adk.tools.litellm_tools.llm
"""

import os
from typing import AsyncGenerator

from google.adk.agents import BaseAgent
from google.adk.events import Event, EventActions
from google.adk.agents.invocation_context import InvocationContext

from underlines_adk.tools.litellm_tools import llm


class QualityAssessmentAgent(BaseAgent):
    """
    Agentic quality assessment agent that trusts LLM intelligence completely.
    
    This agent evaluates research quality against user requirements using LLM judgment
    in natural language. It follows agentic principles by avoiding hardcoded return
    structures and trusting the LLM to make quality determinations.
    
    The agent reads research results and user requirements from session state,
    uses an LLM to evaluate quality, and escalates (terminates the loop) when
    the LLM determines quality is sufficient.
    
    Attributes:
        name: Agent identifier
        description: Human-readable description of agent purpose
    """
    
    def __init__(self, name: str = "QualityAssessmentAgent", description: str | None = None):
        """
        Initialize the QualityAssessmentAgent.
        
        Args:
            name: Unique identifier for this agent instance
            description: Human-readable description of agent purpose
        """
        if description is None:
            description = "Evaluates research quality using LLM intelligence and natural language assessment"
        
        super().__init__(
            name=name,
            description=description,
            sub_agents=[]  # No sub-agents
        )
    
    async def _run_async_impl(self, ctx: InvocationContext) -> AsyncGenerator[Event, None]:
        """
        Evaluate research quality using LLM intelligence and escalate if sufficient.
        
        This method implements the core agentic quality assessment logic:
        1. Validates required inputs with explicit error handling
        2. Uses LLM to evaluate quality in natural language
        3. Determines pass/fail based on LLM's assessment
        4. Escalates (terminates loop) if quality is sufficient
        5. Saves LLM's raw assessment to session state
        
        Args:
            ctx: InvocationContext providing access to session state and other context
            
        Yields:
            Event: Event with quality assessment and escalation decision
            
        Raises:
            ValueError: If required inputs (user_requirements or research_results) are missing
        """
        # MANDATORY: Explicit error handling for missing inputs (Fail Fast Principle)
        user_requirements = ctx.session.state.get("user_requirements", "")

        # Accumulate research results from both WideSearchAgent and DeepSearchAgent
        wide_search_results = ctx.session.state.get("wide_search_results", "")
        deep_search_results = ctx.session.state.get("deep_search_results", "")

        # Combine all available research results
        research_results_parts = []
        if wide_search_results:
            research_results_parts.append(f"Wide Search Results:\n{wide_search_results}")
        if deep_search_results:
            research_results_parts.append(f"Deep Search Results:\n{deep_search_results}")

        research_results = "\n\n".join(research_results_parts)

        # Validate ALL critical inputs with specific error messages
        if not user_requirements:
            raise ValueError("No user requirements found for quality assessment")
        if not research_results:
            raise ValueError("No research results found for quality assessment - neither wide_search_results nor deep_search_results available")
        
        # Get configuration from environment variables
        max_tokens = int(os.getenv("MAX_QUALITY_ASSESSMENT_TOKENS", "1000"))
        
        # Construct LLM prompt for quality evaluation
        assessment_prompt = f"""
        Evaluate this research quality against the user's specific requirements.
        
        USER REQUIREMENTS:
        {user_requirements}
        
        RESEARCH RESULTS:
        {research_results}
        
        EVALUATION TASK:
        Use your professional judgment to determine if this research meets the user's 
        expectations for their specific newsletter type. Consider:
        
        - Does this meet their specified depth and coverage?
        - Are sources credible and relevant to their domain?
        - Is the analysis appropriate for their stated audience?
        - Does this match their quality expectations?
        
        RESPONSE FORMAT:
        Start your response with either "SUFFICIENT" or "NEEDS_IMPROVEMENT" followed 
        by your detailed reasoning. Use your professional judgment - no numerical 
        scoring needed.
        
        Example responses:
        - "SUFFICIENT - The research provides comprehensive coverage of biotech developments with authoritative sources..."
        - "NEEDS_IMPROVEMENT - While the sources are credible, the analysis lacks the technical depth required for a VC audience..."
        """
        
        # Use LLM to evaluate quality in natural language
        try:
            llm_response = await llm.agenerate(
                messages=[{"role": "user", "content": assessment_prompt}],
                max_tokens=max_tokens
            )
            
            # Extract the LLM's assessment
            llm_assessment = llm_response.generations[0][0].text.strip()
            
        except Exception as e:
            # If LLM call fails, raise clear error
            raise ValueError(f"Failed to evaluate research quality: {str(e)}")
        
        # Simple pass/fail determination based on LLM's natural language response
        # Trust the LLM's judgment completely - no complex parsing
        is_sufficient = llm_assessment.upper().startswith("SUFFICIENT")
        
        # Create event with LLM's raw assessment and escalation decision
        yield Event(
            author=self.name,
            actions=EventActions(
                state_delta={"quality_assessment": llm_assessment},
                escalate=is_sufficient  # Escalate (terminate loop) if quality is sufficient
            )
        )
