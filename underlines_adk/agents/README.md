# Agents Module

<!-- purpose-start -->
This module contains AI agents that perform specific tasks using the Google Agent Development Kit. Each agent is configured with specific tools, instructions, and behavior patterns to accomplish different types of web search and information gathering tasks.
<!-- purpose-end -->

<!-- architecture-start -->
## Architecture

Agents are built using the LlmAgent pattern from Google ADK:

```python
agent = LlmAgent(
    name="AgentName",
    model=language_model,
    description="What the agent does",
    instruction="Detailed behavior instructions",
    tools=[tool_functions],
    output_key="structured_output_key"
)
```

Each agent is a self-contained module that can be imported and used independently.
<!-- architecture-end -->

<!-- contents-start -->
## Contents

### Available Agents

- **`wide_search_agent.py`**:
  - **Agent**: `exa_agent` (WideSearchAgent)
  - **Purpose**: Performs broad web searches to gather comprehensive information
  - **Tools**: `exa_wide_search`
  - **Output**: Structured list of stories with metadata

- **`deep_search_agent.py`**:
  - **Agent**: `exa_agent` (DeepSearchAgent)
  - **Purpose**: Conducts comprehensive, in-depth analysis of specific topics with extensive citations
  - **Tools**: `exa_deep_search`
  - **Output**: Detailed markdown analysis with extensive in-line citations

### Agent Configuration

Each agent follows a consistent configuration pattern:
- **Name**: Descriptive identifier for the agent
- **Model**: Language model instance (typically from `litellm_tools`)
- **Description**: Brief summary of agent capabilities
- **Instructions**: Detailed behavior specification
- **Tools**: List of available functions
- **Output Key**: Identifier for extracting structured results
<!-- contents-end -->

## Usage Examples

### WideSearchAgent

```python
from underlines_adk.agents.wide_search_agent import exa_agent
from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService
from google.genai import types

# Set up session and runner
session_service = InMemorySessionService()
runner = Runner(
    agent=exa_agent,
    app_name="search_app",
    session_service=session_service
)

# Create session
session = await session_service.create_session(
    app_name="search_app",
    user_id="user123"
)

# Prepare search query
user_content = types.Content(
    role="user",
    parts=[types.Part(text='{"query": "AI news", "lookback_days": 7}')]
)

# Run the agent
async for event in runner.run_async(
    user_id="user123",
    session_id=session.id,
    new_message=user_content
):
    if event.is_final_response() and event.content:
        results = event.content.parts[0].text
        print("Search results:", results)
```

### Expected Output Format

The WideSearchAgent returns structured JSON with stories:

```json
[
  {
    "title": "Latest AI Breakthrough in Natural Language Processing",
    "reference_urls": [
      "https://example.com/ai-news-1",
      "https://example.com/ai-news-2"
    ],
    "summary": "Researchers have developed a new approach to...",
    "reasoning": "This story is relevant because it discusses recent advances...",
    "date": "2024-01-15"
  }
]
```

## Agent Development

### Creating a New Agent

1. **Create Agent Module**:
```python
# agents/my_new_agent.py
from google.adk.agents.llm_agent import LlmAgent
from ..tools.my_tools import my_tool_function
from ..tools.litellm_tools import llm

my_agent = LlmAgent(
    name="MyNewAgent",
    model=llm,
    description="Brief description of what this agent does",
    instruction="""
    Detailed instructions for the agent:
    1. How to use tools
    2. Expected input format
    3. Required output format
    4. Error handling approach
    """,
    tools=[my_tool_function],
    output_key="my_output_key",
)
```

2. **Add to Package Exports** (if needed):
```python
# agents/__init__.py
from .my_new_agent import my_agent
```

3. **Write Tests**:
```python
# tests/unit/test_my_new_agent.py
def test_my_agent_behavior():
    # Unit tests with mocked tools
    pass

# tests/integration/test_my_new_agent_integration.py  
def test_my_agent_integration():
    # Integration tests with real APIs
    pass
```

### Agent Instruction Best Practices

**Be Specific**:
```python
instruction = """
Use the search_tool to find information about the requested topic.
Format results as a JSON array with these exact fields:
- title: string
- urls: array of strings
- summary: string (max 200 characters)
- date: ISO date string

Stop searching when you have at least 5 relevant results or after 3 search attempts.
"""
```

**Handle Edge Cases**:
```python
instruction = """
If the search tool returns no results:
1. Try a broader search query
2. If still no results, return empty array with explanation

If the search tool fails:
1. Log the error
2. Return partial results if any were obtained
3. Include error information in response
"""
```

**Provide Examples**:
```python
instruction = """
Example input: {"query": "machine learning", "days": 7}
Example output: [{"title": "...", "urls": [...], "summary": "...", "date": "2024-01-15"}]
"""
```

<!-- test-refs-start -->
## Testing

### Unit Tests
Located in `../../tests/unit/`:
- `test_wide_search_agent.py`: Tests for WideSearchAgent behavior
- Mock all external dependencies (APIs, models)
- Test instruction following and output formatting

### Integration Tests  
Located in `../../tests/integration/`:
- `test_wide_search_integration.py`: End-to-end tests with real Exa API
- Require environment variables (EXA_API_KEY)
- Test actual API interactions and response handling

### Running Tests
```bash
# All agent tests
pytest tests/ -k agent

# Specific agent tests
pytest tests/unit/test_wide_search_agent.py
pytest tests/integration/test_wide_search_integration.py

# With coverage
pytest tests/ --cov=underlines_adk.agents
```
<!-- test-refs-end -->

<!-- doc-refs-start -->
## Documentation

- **[Agent Patterns Guide](../../.docs/AGENT_PATTERNS.md)**: Comprehensive guide to agent development
- **[Architecture Overview](../../.docs/ARCHITECTURE.md)**: How agents fit into the system
- **[Workflows Guide](../../.docs/WORKFLOWS.md)**: Development, usage, and maintenance workflows
- **[Main README](../../README.md)**: Project setup and overview
- **[Tools Documentation](../tools/README.md)**: Available tools for agents
<!-- doc-refs-end -->

<!-- dependent-refs-start -->
## Dependencies

### External Dependencies
- **google-adk**: Core agent framework and LlmAgent class
- **litellm**: Language model integration

### Internal Dependencies
- **`../tools/exa_tools`**: Web search functionality
- **`../tools/litellm_tools`**: Language model configuration
- **`../sessions/`**: Session management (when used with runners)
- **`../logging/`**: Event logging (when used with LoggedSessionService)

### Dependents
- **Examples**: `../../examples/` use agents for demonstrations
- **Tests**: Both unit and integration tests depend on agents
- **Applications**: External applications import and use agents
<!-- dependent-refs-end -->

<!-- troubleshooting-start -->
## Troubleshooting

### Agent Not Responding
1. **Check API Keys**: Ensure EXA_API_KEY is set for search agents
2. **Verify Tools**: Confirm tool functions are working independently
3. **Review Instructions**: Check that instructions are clear and actionable
4. **Check Logs**: Use LoggedSessionService to see detailed event logs

### Unexpected Output Format
1. **Review Instructions**: Ensure output format is clearly specified
2. **Add Examples**: Include concrete examples in agent instructions
3. **Test Incrementally**: Test with simple inputs first
4. **Check Tool Outputs**: Verify tools return expected data formats

### Performance Issues
1. **Tool Timeouts**: Check if external APIs are responding slowly
2. **Instruction Length**: Very long instructions can slow processing
3. **Multiple Tool Calls**: Monitor how many times tools are called
4. **Model Selection**: Consider different language models for performance

### Development Issues
```python
# Debug agent behavior
import logging
logging.basicConfig(level=logging.DEBUG)

# Test agent instructions directly
from underlines_adk.agents.wide_search_agent import exa_agent
print("Agent name:", exa_agent.name)
print("Agent description:", exa_agent.description)
print("Agent tools:", [tool.__name__ for tool in exa_agent.tools])
```

### Common Error Messages

**"Tool function not found"**
- Check tool imports in agent module
- Verify tool function names match exactly
- Ensure tools are properly exported from tool modules

**"Invalid instruction format"**
- Instructions should be strings, not other types
- Check for proper string formatting and escaping
- Ensure instructions are not empty

**"Model not configured"**
- Verify `litellm_tools.llm` is properly configured
- Check that required API keys are set for the model
- Ensure model name is valid for the provider
<!-- troubleshooting-end -->
