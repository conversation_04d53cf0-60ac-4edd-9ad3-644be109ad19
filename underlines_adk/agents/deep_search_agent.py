"""
Deep Search Agent for focused, in-depth information analysis.

This module provides the DeepSearchAgent, an AI agent that performs comprehensive,
in-depth analysis of specific stories or topics. Unlike the WideSearchAgent which
gathers broad information, this agent dives deep into specific topics with extensive
citations and detailed analysis.

Key Features:
    - Focused search with full text content analysis
    - Comprehensive multi-perspective analysis
    - Extensive in-line markdown citations
    - Multiple search iterations for thorough coverage
    - Source quality prioritization and cross-referencing

Agent Configuration:
    - Name: "DeepSearchAgent"
    - Model: Gemini 2.5 Flash Preview (via LiteLLM)
    - Tools: exa_deep_search
    - Output: Detailed analysis with extensive citations

Usage:
    from underlines_adk.agents.deep_search_agent import exa_agent
    from google.adk.runners import Runner

    runner = Runner(agent=exa_agent, ...)

    # Query format: {"query": "specific topic", "lookback_days": 30}

Expected Output Format:
    Comprehensive markdown analysis with:
    - Background and context
    - Key facts and developments
    - Multiple perspectives and viewpoints
    - Implications and significance
    - Extensive in-line citations [text](url)

Dependencies:
    - google.adk.agents.llm_agent: Core agent framework
    - ..tools.exa_tools: Exa API integration (exa_deep_search)
    - ..tools.litellm_tools: Language model configuration
"""

from google.adk.agents.llm_agent import LlmAgent

from ..tools.exa_tools import exa_deep_search
from ..tools.litellm_tools import llm

exa_agent: LlmAgent = LlmAgent(
    name="DeepSearchAgent",
    model=llm,
    description="Conducts comprehensive, in-depth analysis adapting to user requirements and target audience.",
    instruction="""
       CONTEXT-AWARE RESEARCH: Read the user's requirements from session state to understand their specific newsletter needs.

       Check session state for:
       - user_requirements: The user's specific newsletter requirements and target audience
       - research_plan: The research plan that guides your analysis strategy
       - current_focus: The specific focus area for this analysis iteration

       ADAPTIVE ANALYSIS STRATEGY: Use the exa_deep_search tool to conduct thorough analysis that matches the user's specific requirements:
       - Adapt analysis depth to their target audience expertise level (VC investors, general readers, technical professionals, etc.)
       - Focus on aspects most relevant to their newsletter type (market implications for VCs, regulatory impacts for fintech, etc.)
       - Match their stated quality expectations and analytical rigor requirements
       - Prioritize sources appropriate for their audience and domain

       COMPREHENSIVE ANALYSIS: Provide detailed analysis that covers (adapted to user requirements):
       - Background and context relevant to their audience
       - Key facts and developments that matter for their newsletter type
       - Multiple perspectives appropriate for their domain (industry experts, regulatory views, market analysis, etc.)
       - Implications and significance for their specific audience
       - Recent updates or developments relevant to their focus areas

       CITATION REQUIREMENTS: Every factual claim MUST be supported with proper markdown citations:
       - Use format: [citation text](source_url) for inline citations
       - Ensure NO factual statement is left uncited
       - Include multiple sources for important claims when available
       - Prioritize source credibility appropriate for the user's audience

       SEARCH STRATEGY: Run multiple searches with refined queries to capture all relevant aspects for the user's specific newsletter type.

       Stop searching when you have:
       - Gathered sufficient depth and breadth for the user's stated requirements
       - Your sources cover perspectives relevant to their target audience
       - You have cross-referenced important claims across multiple authoritative sources
       - The analysis meets their quality expectations and analytical depth requirements

       Ensure your final response is well-structured, thoroughly cited, and provides insights specifically valuable for the user's newsletter type and audience.
       """,
    tools=[exa_deep_search],
    output_key="deep_search_results",
)
