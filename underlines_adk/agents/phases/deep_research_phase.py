"""
Deep Research Phase implementation for DynamicResearchCoordinator.

This module implements Phase 3 of the story-centric workflow: per-story deep
research with quality assessment using LoopAgents.

Following PRD.md architecture:
- Creates LoopAgent per story for deep research
- Uses DeepSearchAgent + QualityAssessmentAgent per story
- Implements per-story quality assessment with escalation
- Aggregates completed story research outputs

Key Features:
- ADK-native LoopAgent coordination per story
- Per-story quality assessment (not global)
- ParallelAgent for concurrent story research
- Universal flexibility for any newsletter type
- Explicit error handling with clear ValueError messages
- Session state management using PRD-defined keys

Anti-Pattern Prevention:
- No hardcoded quality criteria (trusts LLM intelligence)
- No global quality assessment (per-story assessment)
- No custom agent wrappers (uses existing agents directly)
- No silent fallbacks (explicit error handling)
- No newsletter-specific logic (universal flexibility)
"""

import os
from typing import AsyncGenerator
from google.adk.agents import ParallelAgent, LoopAgent
from google.adk.events import Event, EventActions

from google.adk.agents.llm_agent import LlmAgent
from underlines_adk.tools.exa_tools import exa_deep_search
from underlines_adk.tools.litellm_tools import llm
from ..quality_assessment_agent import QualityAssessmentAgent


class DeepResearchPhase:
    """
    Phase 3: Deep Research Per Story with quality assessment.
    
    This class implements the deep research phase of the story-centric workflow,
    creating LoopAgents per story that combine DeepSearchAgent with 
    QualityAssessmentAgent for iterative quality improvement.
    """

    def __init__(self, coordinator_name: str = "DynamicResearchCoordinator"):
        """Initialize the DeepResearchPhase."""
        self.coordinator_name = coordinator_name

    async def execute(self, ctx) -> AsyncGenerator[Event, None]:
        """
        Execute Phase 3: Deep Research Per Story.

        Args:
            ctx: ADK context with session state

        Yields:
            Event: ADK events with state updates

        Raises:
            ValueError: If research fails or required inputs are missing
        """
        # Read from session state (ADK pattern)
        user_requirements = ctx.session.state.get("user_requirements", "")

        # Validate inputs (explicit error handling - MANDATORY)
        if not user_requirements:
            raise ValueError("No user requirements provided for deep research")

        # Get final story list from session state (PRD-defined key)
        final_story_list = ctx.session.state.get("final_story_list", [])
        
        # Explicit error handling for missing stories
        if not final_story_list:
            raise ValueError("No final story list found for deep research")

        # Create LoopAgent for each story (DeepSearchAgent + QualityAssessmentAgent)
        story_research_agents = []
        for i, story in enumerate(final_story_list):
            story_loop = self._create_story_research_loop(ctx, story, i)
            story_research_agents.append(story_loop)

        # Execute parallel per-story research using ADK-native ParallelAgent
        parallel_story_research = ParallelAgent(
            name="ParallelStoryResearch",
            sub_agents=story_research_agents
        )

        # Run all story research loops in parallel
        async for event in parallel_story_research.run_async(ctx):
            yield event

        # Aggregate all story research outputs
        story_research_outputs = await self._aggregate_story_research(ctx)
        
        # Save story research outputs to session state (PRD-defined key)
        yield Event(
            author=self.coordinator_name,
            actions=EventActions(state_delta={"story_research_outputs": story_research_outputs})
        )

    def _create_story_research_loop(self, ctx, story: dict, story_index: int) -> LoopAgent:
        """
        Create LoopAgent with DeepSearchAgent + QualityAssessmentAgent for one story.

        This method creates an ADK-native LoopAgent that combines existing agents
        for iterative deep research with quality assessment per story. Follows ADK
        patterns by reading user_requirements from session state.

        Args:
            ctx: ADK context with session state access
            story: Story dictionary to research
            story_index: Index of the story for naming

        Returns:
            LoopAgent: Configured for per-story research with quality assessment
        """
        # Read user_requirements from session state (ADK pattern)
        user_requirements = ctx.session.state.get("user_requirements", "")

        # Create story-specific quality assessment agent
        story_id = story.get('id', f'story_{story_index}')
        story_title = story.get('title', f'Story {story_index + 1}')

        quality_agent = QualityAssessmentAgent(
            name=f"StoryQualityAssessor_{story_id}",
            description=f"Evaluates research quality for story: {story_title}"
        )

        # Create a new DeepSearchAgent instance for this story (agents can't be reused)
        story_deep_search_agent = LlmAgent(
            name=f"DeepSearchAgent_{story_id}",
            model=llm,
            description="Conducts comprehensive, in-depth analysis adapting to user requirements and target audience.",
            instruction="""
               CONTEXT-AWARE RESEARCH: Read the user's requirements from session state to understand their specific newsletter needs.

               Check session state for:
               - user_requirements: The user's specific newsletter requirements and target audience
               - research_plan: The research plan that guides your analysis strategy
               - current_focus: The specific focus area for this analysis iteration

               ADAPTIVE ANALYSIS STRATEGY: Use the exa_deep_search tool to conduct thorough analysis that matches the user's specific requirements:
               - Adapt analysis depth to their target audience expertise level (VC investors, general readers, technical professionals, etc.)
               - Focus on aspects most relevant to their newsletter type (market implications for VCs, regulatory impacts for fintech, etc.)
               - Match their stated quality expectations and analytical rigor requirements
               - Prioritize sources appropriate for their audience and domain

               COMPREHENSIVE ANALYSIS: Provide detailed analysis that covers (adapted to user requirements):
               - Background and context relevant to their audience
               - Key facts and developments that matter for their newsletter type
               - Multiple perspectives appropriate for their domain (industry experts, regulatory views, market analysis, etc.)
               - Implications and significance for their specific audience
               - Recent updates or developments relevant to their focus areas

               CITATION REQUIREMENTS: Every factual claim MUST be supported with proper markdown citations:
               - Use format: [citation text](source_url) for inline citations
               - Ensure NO factual statement is left uncited
               - Include multiple sources for important claims when available
               - Prioritize source credibility appropriate for the user's audience

               SEARCH STRATEGY: Run multiple searches with refined queries to capture all relevant aspects for the user's specific newsletter type.

               Stop searching when you have:
               - Gathered sufficient depth and breadth for the user's stated requirements
               - Your sources cover perspectives relevant to their target audience
               - You have cross-referenced important claims across multiple authoritative sources
               - The analysis meets their quality expectations and analytical depth requirements

               Ensure your final response is well-structured, thoroughly cited, and provides insights specifically valuable for the user's newsletter type and audience.
               """,
            tools=[exa_deep_search],
            output_key="deep_search_results",
        )

        # ADK-native LoopAgent with new agent instances (no reuse)
        return LoopAgent(
            name=f"StoryResearchLoop_{story_id}",
            sub_agents=[
                story_deep_search_agent,  # New DeepSearchAgent instance for this story
                quality_agent             # Per-story quality assessment with escalation
            ],
            max_iterations=int(os.getenv("MAX_STORY_RESEARCH_ITERATIONS", "3"))
        )

    async def _aggregate_story_research(self, ctx) -> list[dict]:
        """
        Aggregate completed research for all stories.
        
        This method collects and aggregates the completed research outputs from
        all story research LoopAgents. For the initial implementation, this
        provides a simple aggregation that can be enhanced in future iterations.
        
        Args:
            ctx: ADK context with session state
            
        Returns:
            list[dict]: Aggregated research outputs for all stories
        """
        # Simple aggregation for initial implementation
        # In future iterations, this would:
        # - Collect research outputs from each story LoopAgent
        # - Ensure all stories have completed research
        # - Format consistently for newsletter synthesis
        # - Include quality assessment results
        
        # For now, return empty list as placeholder
        # This will be enhanced when implementing the full research aggregation logic
        return []
