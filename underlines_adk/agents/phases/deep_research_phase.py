"""
Deep Research Phase implementation for DynamicResearchCoordinator.

This module implements Phase 3 of the story-centric workflow: per-story deep
research with quality assessment using LoopAgents.

Following PRD.md architecture:
- Creates LoopAgent per story for deep research
- Uses DeepSearchAgent + QualityAssessmentAgent per story
- Implements per-story quality assessment with escalation
- Aggregates completed story research outputs

Key Features:
- ADK-native LoopAgent coordination per story
- Per-story quality assessment (not global)
- ParallelAgent for concurrent story research
- Universal flexibility for any newsletter type
- Explicit error handling with clear ValueError messages
- Session state management using PRD-defined keys

Anti-Pattern Prevention:
- No hardcoded quality criteria (trusts LLM intelligence)
- No global quality assessment (per-story assessment)
- No custom agent wrappers (uses existing agents directly)
- No silent fallbacks (explicit error handling)
- No newsletter-specific logic (universal flexibility)
"""

import os
from typing import AsyncGenerator
from google.adk.agents import ParallelAgent, LoopAgent
from google.adk.events import Event, EventActions

from ..deep_search_agent import exa_agent as deep_search_agent
from ..quality_assessment_agent import QualityAssessmentAgent


class DeepResearchPhase:
    """
    Phase 3: Deep Research Per Story with quality assessment.
    
    This class implements the deep research phase of the story-centric workflow,
    creating LoopAgents per story that combine DeepSearchAgent with 
    QualityAssessmentAgent for iterative quality improvement.
    """

    def __init__(self, coordinator_name: str = "DynamicResearchCoordinator"):
        """Initialize the DeepResearchPhase."""
        self.coordinator_name = coordinator_name

    async def execute(self, ctx, user_requirements: str) -> AsyncGenerator[Event, None]:
        """
        Execute Phase 3: Deep Research Per Story.
        
        Args:
            ctx: ADK context with session state
            user_requirements: Natural language newsletter requirements
            
        Yields:
            Event: ADK events with state updates
            
        Raises:
            ValueError: If research fails or required inputs are missing
        """
        # Validate inputs (explicit error handling - MANDATORY)
        if not user_requirements:
            raise ValueError("No user requirements provided for deep research")

        # Get final story list from session state (PRD-defined key)
        final_story_list = ctx.session.state.get("final_story_list", [])
        
        # Explicit error handling for missing stories
        if not final_story_list:
            raise ValueError("No final story list found for deep research")

        # Create LoopAgent for each story (DeepSearchAgent + QualityAssessmentAgent)
        story_research_agents = []
        for i, story in enumerate(final_story_list):
            story_loop = self._create_story_research_loop(story, i, user_requirements)
            story_research_agents.append(story_loop)

        # Execute parallel per-story research using ADK-native ParallelAgent
        parallel_story_research = ParallelAgent(
            name="ParallelStoryResearch",
            sub_agents=story_research_agents
        )

        # Run all story research loops in parallel
        async for event in parallel_story_research.run_async(ctx):
            yield event

        # Aggregate all story research outputs
        story_research_outputs = await self._aggregate_story_research(ctx)
        
        # Save story research outputs to session state (PRD-defined key)
        yield Event(
            author=self.coordinator_name,
            actions=EventActions(state_delta={"story_research_outputs": story_research_outputs})
        )

    def _create_story_research_loop(self, story: dict, story_index: int, user_requirements: str) -> LoopAgent:
        """
        Create LoopAgent with DeepSearchAgent + QualityAssessmentAgent for one story.
        
        This method creates an ADK-native LoopAgent that combines existing agents
        for iterative deep research with quality assessment per story.
        
        Args:
            story: Story dictionary to research
            story_index: Index of the story for naming
            user_requirements: Natural language newsletter requirements
            
        Returns:
            LoopAgent: Configured for per-story research with quality assessment
        """
        # Create story-specific quality assessment agent
        story_id = story.get('id', f'story_{story_index}')
        story_title = story.get('title', f'Story {story_index + 1}')
        
        quality_agent = QualityAssessmentAgent(
            name=f"StoryQualityAssessor_{story_id}",
            description=f"Evaluates research quality for story: {story_title}"
        )

        # ADK-native LoopAgent with existing agents (no custom wrappers)
        return LoopAgent(
            name=f"StoryResearchLoop_{story_id}",
            sub_agents=[
                deep_search_agent,  # Existing DeepSearchAgent reads session state natively
                quality_agent       # Per-story quality assessment with escalation
            ],
            max_iterations=int(os.getenv("MAX_STORY_RESEARCH_ITERATIONS", "3"))
        )

    async def _aggregate_story_research(self, ctx) -> list[dict]:
        """
        Aggregate completed research for all stories.
        
        This method collects and aggregates the completed research outputs from
        all story research LoopAgents. For the initial implementation, this
        provides a simple aggregation that can be enhanced in future iterations.
        
        Args:
            ctx: ADK context with session state
            
        Returns:
            list[dict]: Aggregated research outputs for all stories
        """
        # Simple aggregation for initial implementation
        # In future iterations, this would:
        # - Collect research outputs from each story LoopAgent
        # - Ensure all stories have completed research
        # - Format consistently for newsletter synthesis
        # - Include quality assessment results
        
        # For now, return empty list as placeholder
        # This will be enhanced when implementing the full research aggregation logic
        return []
