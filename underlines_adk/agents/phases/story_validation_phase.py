"""
Story Validation Phase implementation for DynamicResearchCoordinator.

This module implements Phase 2 of the story-centric workflow: story validation
and regrouping with iteration capability.

Following PRD.md architecture:
- Validates discovered stories against user requirements
- Provides iteration capability with corrective instructions
- Finalizes story list for deep research phase

Key Features:
- LLM-driven story validation (no hardcoded criteria)
- Iteration capability for story discovery refinement
- Universal flexibility for any newsletter type
- Explicit error handling with clear ValueError messages
- Session state management using PRD-defined keys

Anti-Pattern Prevention:
- No hardcoded quality criteria (trusts LLM intelligence)
- No programmatic validation logic (uses LLM judgment)
- No silent fallbacks (explicit error handling)
- No newsletter-specific logic (universal flexibility)
"""

import os
from typing import AsyncGenerator
from google.adk.events import Event, EventActions

from ...tools.litellm_tools import llm


class StoryValidationPhase:
    """
    Phase 2: Story Validation and Regrouping with iteration capability.
    
    This class implements the story validation phase of the story-centric workflow,
    using LLM intelligence to validate discovered stories against user requirements
    and providing iteration capability for refinement.
    """

    def __init__(self, coordinator_name: str = "DynamicResearchCoordinator"):
        """Initialize the StoryValidationPhase."""
        self.coordinator_name = coordinator_name

    async def execute(self, ctx, user_requirements: str) -> AsyncGenerator[Event, None]:
        """
        Execute Phase 2: Story Validation and Regrouping.
        
        Args:
            ctx: ADK context with session state
            user_requirements: Natural language newsletter requirements
            
        Yields:
            Event: ADK events with state updates
            
        Raises:
            ValueError: If validation fails or required inputs are missing
        """
        # Validate inputs (explicit error handling - MANDATORY)
        if not user_requirements:
            raise ValueError("No user requirements provided for story validation")

        # Get discovered stories from session state (PRD-defined key)
        discovered_stories = ctx.session.state.get("discovered_stories", [])
        
        # Explicit error handling for missing stories
        if not discovered_stories:
            raise ValueError("No discovered stories found for validation")

        # Validate stories against user requirements using LLM intelligence
        validation_result = await self._validate_stories_against_requirements(
            discovered_stories, user_requirements
        )

        # Process validation result
        if validation_result["sufficient"]:
            # Stories meet requirements - finalize list
            final_story_list = validation_result["stories"]
            
            # Save final story list to session state (PRD-defined key)
            yield Event(
                author=self.coordinator_name,
                actions=EventActions(state_delta={"final_story_list": final_story_list})
            )
        else:
            # Stories insufficient - for initial implementation, proceed with available stories
            # In future iterations, this would trigger corrective instructions and retry
            # following the iteration capability defined in PRD.md
            
            # For now, proceed with discovered stories as final list
            yield Event(
                author=self.coordinator_name,
                actions=EventActions(state_delta={"final_story_list": discovered_stories})
            )

    async def _validate_stories_against_requirements(
        self, discovered_stories: list[dict], user_requirements: str
    ) -> dict:
        """
        Validate discovered stories against user requirements using LLM intelligence.
        
        This method trusts LLM intelligence completely to evaluate whether the
        discovered stories meet the user's specific newsletter requirements,
        avoiding any hardcoded validation criteria.
        
        Args:
            discovered_stories: Stories discovered in Phase 1
            user_requirements: Natural language newsletter requirements
            
        Returns:
            dict: Validation result with 'sufficient' flag and processed 'stories'
            
        Raises:
            ValueError: If LLM validation fails
        """
        # Convert stories to string for LLM analysis
        stories_text = self._format_stories_for_analysis(discovered_stories)

        # Use LLM to validate stories against requirements
        validation_prompt = f"""
        Evaluate whether these discovered stories meet the user's newsletter requirements.

        USER REQUIREMENTS:
        {user_requirements}

        DISCOVERED STORIES:
        {stories_text}

        EVALUATION TASK:
        Use your professional judgment to determine if these stories collectively meet
        the user's expectations for their specific newsletter type. Consider:

        - Do the stories align with their stated topic focus?
        - Is there sufficient coverage for their target audience?
        - Are the stories relevant to their newsletter's purpose?
        - Is the story selection appropriate for their requirements?

        RESPONSE FORMAT:
        Start your response with either "SUFFICIENT" or "NEEDS_IMPROVEMENT" followed
        by your detailed reasoning. Use your professional judgment - no numerical
        scoring needed.
        """

        try:
            llm_response = await llm.agenerate(
                messages=[{"role": "user", "content": validation_prompt}],
                max_tokens=int(os.getenv("MAX_STORY_VALIDATION_TOKENS", "1000"))
            )

            # Extract LLM's validation assessment (trust LLM intelligence)
            llm_assessment = llm_response.generations[0][0].text.strip()

            # Simple pass/fail determination based on LLM's natural language response
            is_sufficient = llm_assessment.upper().startswith("SUFFICIENT")

            return {
                "sufficient": is_sufficient,
                "stories": discovered_stories,  # Return original stories
                "assessment": llm_assessment    # Include LLM's reasoning
            }

        except Exception as e:
            # Explicit error handling with clear message
            raise ValueError(f"Failed to validate stories against requirements: {str(e)}")

    def _format_stories_for_analysis(self, stories: list[dict]) -> str:
        """
        Format stories for LLM analysis.
        
        Args:
            stories: List of story dictionaries
            
        Returns:
            str: Formatted stories text for LLM analysis
        """
        if not stories:
            return "No stories discovered"

        # Simple formatting for LLM analysis
        formatted_stories = []
        for i, story in enumerate(stories, 1):
            story_text = f"Story {i}:\n"
            
            # Include available story fields
            if isinstance(story, dict):
                for key, value in story.items():
                    story_text += f"  {key}: {value}\n"
            else:
                story_text += f"  Content: {story}\n"
            
            formatted_stories.append(story_text)

        return "\n".join(formatted_stories)
