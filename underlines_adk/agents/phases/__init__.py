"""
Phase implementations for the DynamicResearchCoordinator.

This package contains modular phase implementations that follow the story-centric
workflow architecture defined in PRD.md. Each phase is implemented as a separate
class to improve maintainability and testability.

Phases:
    - StoryDiscoveryPhase: Phase 1 - Topic analysis and parallel story discovery
    - StoryValidationPhase: Phase 2 - Story validation and regrouping
    - DeepResearchPhase: Phase 3 - Per-story deep research with quality assessment

All phases follow ADK-native patterns and the anti-patterns guidelines from
ANTI_PATTERNS_AND_VALIDATION.md.
"""

from .story_discovery_phase import StoryDiscoveryPhase
from .story_validation_phase import StoryValidationPhase
from .deep_research_phase import DeepResearchPhase

__all__ = [
    "StoryDiscoveryPhase",
    "StoryValidationPhase", 
    "DeepResearchPhase"
]
