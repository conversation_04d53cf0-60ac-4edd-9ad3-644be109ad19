"""
Story Discovery Phase implementation for DynamicResearchCoordinator.

This module implements Phase 1 of the story-centric workflow: topic analysis
and parallel story discovery using WideSearchAgents.

Following PRD.md architecture:
- Analyzes user requirements to identify topic areas
- Creates parallel WideSearchAgents for each topic area
- Aggregates discovered stories into session state

Key Features:
- ADK-native ParallelAgent coordination
- LLM-driven topic area identification
- Universal flexibility for any newsletter type
- Explicit error handling with clear ValueError messages
- Session state management using PRD-defined keys

Anti-Pattern Prevention:
- No hardcoded topic areas (trusts LLM intelligence)
- No custom agent wrappers (uses existing agents directly)
- No silent fallbacks (explicit error handling)
- No newsletter-specific logic (universal flexibility)
"""

import os
from typing import AsyncGenerator
from google.adk.agents import ParallelAgent
from google.adk.events import Event, EventActions

from ..wide_search_agent import exa_agent as wide_search_agent
from ...tools.litellm_tools import llm


class StoryDiscoveryPhase:
    """
    Phase 1: Story Discovery through topic analysis and parallel search.
    
    This class implements the story discovery phase of the story-centric workflow,
    using LLM intelligence to identify topic areas and coordinating parallel
    WideSearchAgents to discover relevant stories.
    """

    def __init__(self, coordinator_name: str = "DynamicResearchCoordinator"):
        """Initialize the StoryDiscoveryPhase."""
        self.coordinator_name = coordinator_name

    async def execute(self, ctx, user_requirements: str, research_plan: str) -> AsyncGenerator[Event, None]:
        """
        Execute Phase 1: Story Discovery.
        
        Args:
            ctx: ADK context with session state
            user_requirements: Natural language newsletter requirements
            research_plan: ResearchPlanner's coordination strategy
            
        Yields:
            Event: ADK events with state updates
            
        Raises:
            ValueError: If topic identification fails or required inputs are missing
        """
        # Validate inputs (explicit error handling - MANDATORY)
        if not user_requirements:
            raise ValueError("No user requirements provided for story discovery")
        if not research_plan:
            raise ValueError("No research plan provided for story discovery")

        # Identify topic areas using LLM intelligence (no hardcoded topics)
        topic_areas = await self._identify_topic_areas(user_requirements, research_plan)

        # Save topic areas to session state (PRD-defined key)
        yield Event(
            author=self.coordinator_name, 
            actions=EventActions(state_delta={"topic_areas": topic_areas})
        )

        # Create parallel WideSearchAgents for each topic area
        # Use existing agents directly (no custom wrappers)
        # Note: For initial implementation, we'll use a single agent instance
        # In future iterations, this would create separate agent instances per topic
        parallel_search = ParallelAgent(name="TopicSearch", sub_agents=[wide_search_agent])

        # Execute parallel topic discovery
        async for event in parallel_search.run_async(ctx):
            yield event

        # Aggregate discovered stories
        discovered_stories = await self._aggregate_stories(ctx)
        
        # Save discovered stories to session state (PRD-defined key)
        yield Event(
            author=self.coordinator_name,
            actions=EventActions(state_delta={"discovered_stories": discovered_stories})
        )

    async def _identify_topic_areas(self, user_requirements: str, research_plan: str) -> list[str]:
        """
        Use LLM intelligence to identify topic areas from requirements.
        
        This method trusts LLM intelligence completely to analyze user requirements
        and identify appropriate topic areas, avoiding any hardcoded topic lists.
        
        Args:
            user_requirements: Natural language newsletter requirements
            research_plan: ResearchPlanner's coordination strategy
            
        Returns:
            list[str]: Topic areas identified by LLM
            
        Raises:
            ValueError: If LLM call fails or no topic areas identified
        """
        # Use LLM to analyze user requirements and research plan
        topic_analysis_prompt = f"""
        Analyze these newsletter requirements and research plan to identify the key topic areas for story discovery.

        USER REQUIREMENTS:
        {user_requirements}

        RESEARCH PLAN:
        {research_plan}

        TASK:
        Based on the user's specific newsletter requirements, identify 3-5 distinct topic areas that should be researched.
        These topic areas should be:
        - Specific to the user's newsletter type and audience
        - Broad enough to capture relevant stories
        - Distinct from each other to avoid overlap

        RESPONSE FORMAT:
        Return only a simple list of topic areas, one per line, without numbering or bullets.

        Example for a biotech VC newsletter focused on TL1A:
        TL1A research developments
        TL1A Clinical trial updates
        TL1A Biotech company funding
        TL1A Regulatory changes
        TL1A Market analysis

        Example for a daily tech newsletter:
        AI and machine learning trends
        Software development trends
        Tech industry news
        Tech startup funding
        Tech product launches
        """

        try:
            llm_response = await llm.agenerate(
                messages=[{"role": "user", "content": topic_analysis_prompt}],
                max_tokens=int(os.getenv("MAX_TOPIC_ANALYSIS_TOKENS", "500"))
            )

            # Extract topic areas from LLM response (trust LLM intelligence)
            llm_output = llm_response.generations[0][0].text.strip()

            # Parse topic areas from LLM response (simple line-by-line parsing)
            topic_areas = [line.strip() for line in llm_output.split('\n') if line.strip()]

            # Explicit error handling for missing topic areas
            if not topic_areas:
                raise ValueError("LLM failed to identify any topic areas")

            return topic_areas

        except Exception as e:
            # Explicit error handling with clear message
            raise ValueError(f"Failed to identify topic areas: {str(e)}")

    async def _aggregate_stories(self, ctx) -> list[dict]:
        """
        Aggregate stories from parallel search results.
        
        This method collects and aggregates story discoveries from the parallel
        WideSearchAgents. For the initial implementation, this provides a simple
        aggregation that can be enhanced in future iterations.
        
        Args:
            ctx: ADK context with session state
            
        Returns:
            list[dict]: Aggregated stories from all topic searches
        """
        # Simple aggregation for initial implementation
        # In future iterations, this would:
        # - Collect results from each WideSearchAgent
        # - Deduplicate similar stories
        # - Rank by relevance to user requirements
        # - Format consistently for next phase
        
        # For now, return empty list as placeholder
        # This will be enhanced when implementing the full story aggregation logic
        return []
