"""
Logging utilities for the Underlines ADK project.

This module provides event logging functionality for capturing and serializing
Google ADK events to JSON Lines files. It enables comprehensive audit trails,
debugging, and analysis of agent interactions.

Key Functions:
    - log_event_to_json_file: Main function for logging events to files

Features:
    - Complete event serialization including content, actions, and metadata
    - JSON Lines format for easy parsing and streaming
    - UTC timestamps for consistent timezone handling
    - Graceful error handling and recovery
    - Session isolation with separate files per session

File Format:
    - Location: session_logs/{session_id}.jsonl
    - Format: One JSON object per line
    - Encoding: UTF-8
    - Content: Complete event data with metadata

Usage:
    from underlines_adk.logging import log_event_to_json_file

    # Log an event (typically called by LoggedSessionService)
    log_event_to_json_file(session_id, event)

Configuration:
    - LOG_DIRECTORY: Can be modified in json_file_logger module
    - Default location: "session_logs" in current working directory

Dependencies:
    - google.adk.events: Event objects for serialization
    - Standard library: json, logging, datetime, pathlib
"""

from .json_file_logger import log_event_to_json_file

__all__ = ["log_event_to_json_file"]
