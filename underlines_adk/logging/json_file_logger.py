"""
JSON Lines file logging for Google ADK events.

This module provides low-level utilities for serializing Google ADK Event objects
and writing them to JSON Lines files. It handles the complex task of converting
event objects with nested content into JSON-serializable dictionaries while
preserving all relevant information.

Key Functions:
    - log_event_to_json_file: Main function for logging events to files
    - _serialize_event: Internal utility for event serialization

Features:
    - Complete event serialization including content, actions, and metadata
    - JSON Lines format for easy parsing and streaming
    - UTC timestamps for consistent timezone handling
    - Graceful error handling and recovery
    - Session isolation with separate files per session

File Format:
    - Location: {LOG_DIRECTORY}/{session_id}.jsonl
    - Format: One JSON object per line
    - Encoding: UTF-8
    - Structure: Comprehensive event data with metadata

Configuration:
    - LOG_DIRECTORY: Directory for log files (default: "session_logs")
    - Can be modified by changing the module-level constant

Usage:
    from underlines_adk.logging import log_event_to_json_file

    # Log an event (typically called by LoggedSessionService)
    log_event_to_json_file(session_id, event)

Error Handling:
    - Serialization failures are caught and logged
    - File write errors are logged but don't raise exceptions
    - Partial event data is preserved when possible
    - System continues operation even if logging fails

Dependencies:
    - google.adk.events: Event objects for serialization
    - Standard library: json, logging, datetime, pathlib
"""

import json
import logging
from datetime import UTC, datetime
from pathlib import Path
from typing import Any

from google.adk.events import Event

# Directory where session log files are stored
# Can be modified to change log location
LOG_DIRECTORY = "session_logs"

# Logger for this module's internal errors
logger = logging.getLogger(__name__)


def _serialize_event(event: Event) -> dict[str, Any]:
    """
    Convert a Google ADK Event object to a JSON-serializable dictionary.

    This function handles the complex task of extracting all relevant information
    from an ADK Event object and converting it to a format that can be safely
    serialized to JSON. It preserves event metadata, content (including text,
    function calls, and responses), actions, and error information.

    The serialization process is designed to be robust and handle various event
    types and content structures. If serialization fails for any reason, a
    minimal error event is returned to ensure logging continues.

    Args:
        event: The Google ADK Event object to serialize. Contains metadata,
            content, actions, and potentially error information.

    Returns:
        Dictionary with the following structure:
        {
            "timestamp_utc": "2024-01-15T10:30:45.123456Z",
            "event_id": "unique_event_identifier",
            "author": "agent_name_or_user",
            "invocation_id": "invocation_identifier",
            "is_final_response": bool,
            "turn_complete": bool,
            "content": {
                "role": "user|assistant|function",
                "parts": [
                    {
                        "text": "text_content",
                        "function_call": {
                            "name": "function_name",
                            "args": {...}
                        },
                        "function_response": {
                            "name": "function_name",
                            "response": {...}
                        }
                    }
                ]
            },
            "actions": {
                "state_delta": ...,
                "artifact_delta": ...,
                "transfer_to_agent": ...,
                "escalate": bool,
                "skip_summarization": bool
            },
            "error_code": "error_code_if_any",
            "error_message": "error_message_if_any"
        }

    Note:
        - Timestamp is always in UTC ISO format
        - Content and actions may be None if not present in the event
        - Function calls and responses are preserved with full argument/response data
        - Error information is included when present

    Error Handling:
        If serialization fails, returns a minimal error event:
        {
            "timestamp_utc": "current_time",
            "error": "Failed to serialize event",
            "event_id": "original_event_id",
            "exception": "error_description"
        }
    """
    try:
        event_data = {
            "timestamp_utc": datetime.now(UTC).isoformat(),
            "event_id": event.id,
            "author": event.author,
            "invocation_id": event.invocation_id,
            "is_final_response": event.is_final_response(),
            "turn_complete": event.turn_complete,
            "content": None,
            "actions": None,
            "error_code": event.error_code,
            "error_message": event.error_message,
        }
        if event.content:
            parts_data = []
            for part in event.content.parts:
                part_data = {}
                if part.text:
                    part_data["text"] = part.text
                if fn_call := part.function_call:
                    part_data["function_call"] = {
                        "name": fn_call.name,
                        "args": fn_call.args,
                    }
                if fn_response := part.function_response:
                    part_data["function_response"] = {
                        "name": fn_response.name,
                        "response": fn_response.response,
                    }
                parts_data.append(part_data)
            event_data["content"] = {"role": event.content.role, "parts": parts_data}
        if event.actions:
            event_data["actions"] = {
                "state_delta": event.actions.state_delta,
                "artifact_delta": event.actions.artifact_delta,
                "transfer_to_agent": event.actions.transfer_to_agent,
                "escalate": event.actions.escalate,
                "skip_summarization": event.actions.skip_summarization,
            }
        return event_data
    except Exception as e:
        logger.error(f"Error serializing event {event.id}: {e}")
        return {
            "timestamp_utc": datetime.now(UTC).isoformat(),
            "error": "Failed to serialize event",
            "event_id": event.id,
            "exception": str(e),
        }


def log_event_to_json_file(session_id: str, event: Event) -> None:
    """
    Log a single ADK event to a JSON Lines file for the specified session.

    This is the main entry point for event logging. It handles file creation,
    directory setup, event serialization, and error recovery. Each session
    gets its own log file in the format {LOG_DIRECTORY}/{session_id}.jsonl.

    The function is designed to be non-blocking and fault-tolerant. If logging
    fails for any reason, the error is logged to the Python logging system
    but no exception is raised, ensuring that agent operations continue normally.

    File Operations:
        - Creates LOG_DIRECTORY if it doesn't exist
        - Creates or appends to {session_id}.jsonl
        - Uses UTF-8 encoding for international character support
        - Appends one JSON object per line (JSON Lines format)

    Args:
        session_id: Unique identifier for the session. Used as the log filename.
            Must be a valid filename (no path separators or special characters).
            If empty or None, the event is not logged and a warning is issued.
        event: The Google ADK Event object to log. Will be serialized using
            _serialize_event() before writing to file.

    Returns:
        None. This function performs side effects (file writing) only.

    Side Effects:
        - Creates LOG_DIRECTORY if it doesn't exist
        - Creates or appends to log file
        - Logs errors to Python logging system if failures occur

    Error Handling:
        - Missing session_id: Warning logged, no file operation
        - Directory creation failure: Error logged, operation aborted
        - File write failure: Error logged, operation aborted
        - Serialization failure: Handled by _serialize_event()

    Example:
        >>> from google.adk.events import Event
        >>> log_event_to_json_file("session_123", some_event)
        # Creates/appends to session_logs/session_123.jsonl

    Note:
        This function is typically called by LoggedSessionService rather than
        directly by user code. The session_id should match the session ID
        used by the session service for consistency.
    """
    if not session_id:
        logger.warning("Attempted to log an event with no session ID.")
        return
    try:
        log_dir = Path(LOG_DIRECTORY)
        log_dir.mkdir(exist_ok=True)
        log_file = log_dir / f"{session_id}.jsonl"
        event_data = _serialize_event(event)
        event_data["session_id"] = session_id

        with open(log_file, "a", encoding="utf-8") as f:
            json.dump(event_data, f, ensure_ascii=False)
            f.write("\n")
    except Exception as e:
        logger.error(f"Failed to log event for session {session_id}: {e}")
