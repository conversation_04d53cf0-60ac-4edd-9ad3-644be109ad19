"""
Underlines ADK (Agent Development Kit).

This package provides intelligent web search agents powered by the Exa API and
Google's Agent Development Kit. It enables developers to build applications that
can gather, analyze, and structure information from across the web using AI agents.

Key Components:
    - agents: AI agents for different search strategies (WideSearchAgent, DeepSearchAgent)
    - tools: External API integrations (Exa, LiteLLM)
    - sessions: Session management with automatic logging
    - logging: Event logging utilities for audit trails
    - callbacks: Alternative event handling approach

Usage:
    from underlines_adk.agents.wide_search_agent import exa_agent
    from underlines_adk.sessions import LoggedSessionService
    from google.adk.runners import Runner

    # Create session service with logging
    session_service = LoggedSessionService()

    # Create and run agent
    runner = Runner(
        agent=exa_agent,
        app_name="my_search_app",
        session_service=session_service
    )

Dependencies:
    - google-adk: Core agent framework
    - exa-py: Exa API client for web search
    - litellm: Multi-provider LLM integration

Environment Variables:
    - EXA_API_KEY: Required for Exa API access
    - GOOGLE_API_KEY: Required for Gemini model access (via LiteLLM)
"""

__version__ = "0.0.1"
__author__ = "<PERSON>"
__email__ = "<EMAIL>"

# Package metadata
__all__ = [
    "__version__",
    "__author__",
    "__email__",
]