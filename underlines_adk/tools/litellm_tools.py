"""
Language model integration via LiteLLM.

This module provides a configured language model instance for use by agents
in the Underlines ADK. It uses LiteLLM to provide a unified interface across
multiple language model providers while currently being configured for OpenAI's
GPT-4o-mini model.

Key Features:
    - Multi-provider support via LiteLLM
    - Consistent interface across different models
    - Easy model switching and configuration
    - Integration with Google ADK's model framework

Current Configuration:
    - Provider: Google (Gemini)
    - Model: gemini-2.5-flash-preview-05-20
    - Interface: Google ADK LiteLlm wrapper

Usage:
    from underlines_adk.tools.litellm_tools import llm
    from google.adk.agents.llm_agent import LlmAgent

    # Use in agent configuration
    agent = LlmAgent(
        name="MyAgent",
        model=llm,  # Pre-configured model instance
        description="Agent description",
        instruction="Agent instructions",
        tools=[...],
    )

Model Selection:
    The current model (gemini-2.5-flash-preview-05-20) is chosen for:
    - Excellent performance on large context processing
    - Superior handling of complex analysis tasks
    - Fast response times for interactive applications
    - Strong capability for deep search result processing
    - Cost efficiency for development and production

Environment Variables:
    - GOOGLE_API_KEY: Required for Gemini model access
    - Additional provider keys as needed for other models

Dependencies:
    - google.adk.models.lite_llm: Google ADK's LiteLLM wrapper
    - litellm: Multi-provider LLM integration library

Note:
    To change the model, modify the model parameter in the LiteLlm constructor.
    Supported formats include "openai/gpt-4", "anthropic/claude-3", etc.
    See LiteLLM documentation for full provider and model support.
"""

from google.adk.models.lite_llm import LiteLlm

# Pre-configured language model instance
# Used by all agents in the Underlines ADK
# Using Gemini 2.5 Flash Preview for better handling of large contexts in deep search tasks
llm = LiteLlm(
    model="gemini/gemini-2.5-flash-preview-05-20"
    # model="openai/gpt-4o",
)
